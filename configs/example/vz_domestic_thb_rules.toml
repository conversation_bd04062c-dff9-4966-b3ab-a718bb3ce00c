# VZ境内线A舱固定使用THB进行查询预定配置
# 定义：境内线的意思是出发、到达都是泰国境内机场

[vz_domestic_thb_rules]
# 是否启用VZ境内线THB规则
enabled = true

# 适用的航空公司代码
airline_codes = ["VZ"]

# 适用的舱位等级
cabin_classes = ["A"]  # A舱

# 强制使用的币种
force_currency = "THB"

# 泰国境内线路列表（从提供的数据中筛选出的境内线）
# 基于提供的航线数据，以下是泰国境内的航线（共21条）：
domestic_routes = [
    "BKK-CEI",  # 曼谷素万那普-清莱
    "BKK-CNX",  # 曼谷素万那普-清迈
    "BKK-HDY",  # 曼谷素万那普-合艾
    "BKK-HKT",  # 曼谷素万那普-普吉
    "BKK-KBV",  # 曼谷素万那普-甲米
    "BKK-KKC",  # 曼谷素万那普-孔敬
    "BKK-UBP",  # 曼谷素万那普-乌汶
    "BKK-URT",  # 曼谷素万那普-素叻他尼
    "BKK-UTH",  # 曼谷素万那普-乌隆他尼
    "CEI-BKK",  # 清莱-曼谷素万那普
    "CEI-HKT",  # 清莱-普吉
    "CNX-BKK",  # 清迈-曼谷素万那普
    "CNX-HKT",  # 清迈-普吉
    "HDY-BKK",  # 合艾-曼谷素万那普
    "HKT-BKK",  # 普吉-曼谷素万那普
    "HKT-CNX",  # 普吉-清迈
    "KBV-BKK",  # 甲米-曼谷素万那普
    "KKC-BKK",  # 孔敬-曼谷素万那普
    "UBP-BKK",  # 乌汶-曼谷素万那普
    "URT-BKK",  # 素叻他尼-曼谷素万那普
    "UTH-BKK"   # 乌隆他尼-曼谷素万那普
]

# 泰国机场代码列表（用于动态判断境内线）
thailand_airports = [
    # 国际机场
    "BKK", "DMK", "BTZ", "CNX", "CEI", "HDY", "KKC", "USM", "KBV", "NST", "HKT", "UTP", "URT", "UTH",
    # 国内机场
    "BFV", "CJM", "HHQ", "LPT", "LOE", "HGN", "MAQ", "KOP", "NAK", "NNT", "NAW", "PHY", "PHS", "PRH",
    "UNN", "ROI", "SNO", "THS", "TST", "TDX", "UBP",
    # 军用/私人/特殊用途机场（仍属泰国境内）
    "QHI", "KDT", "KKM", "TKH", "PYY", "PAN", "PHZ", "SGZ", "PXR", "TKT", "UTR"
]

# 规则说明
[vz_domestic_thb_rules.description]
purpose = "VZ境内线A舱固定使用THB进行查询预定"
definition = "境内线的意思是出发、到达都是泰国境内机场"
implementation = "在调用book前，查询航班时，判断是否符合条件，符合则强制用THB重新查询，然后再走book流程"
