#!/usr/bin/env python3
"""
测试agent_service.py中VZ境内线A舱THB规则功能
"""
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.helper_service import should_use_thb_for_vz_domestic


def test_vz_thb_rule_logic():
    """测试VZ境内线A舱THB规则逻辑"""
    print("=== 测试VZ境内线A舱THB规则逻辑 ===")

    test_cases = [
        {
            'name': 'VZ境内线A舱 - 应该使用THB',
            'task_data': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A',
                'currency_code': 'USD',
            },
            'expected': True,
        },
        {
            'name': 'VZ境内线无舱位指定 - 应该使用THB',
            'task_data': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'HKT',
                'currency_code': 'USD',
            },
            'expected': True,
        },
        {
            'name': 'VJ航空 - 不应该使用THB',
            'task_data': {
                'airline_code': 'VJ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A',
                'currency_code': 'USD',
            },
            'expected': False,
        },
        {
            'name': 'VZ国际线 - 不应该使用THB',
            'task_data': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'SGN',
                'cabin_class': 'A',
                'currency_code': 'USD',
            },
            'expected': False,
        },
        {
            'name': 'VZ境内线Y舱 - 不应该使用THB',
            'task_data': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'Y',
                'currency_code': 'USD',
            },
            'expected': False,
        },
    ]

    for case in test_cases:
        print(f"\n  测试: {case['name']}")
        result = should_use_thb_for_vz_domestic(case['task_data'])
        status = "✓" if result == case['expected'] else "✗"
        print(f"    {status} 规则判断: {result} (期望: {case['expected']})")


def simulate_agent_service_logic():
    """模拟agent_service.py中的逻辑"""
    print("\n=== 模拟agent_service.py中的逻辑 ===")

    # 模拟获取到的航班信息
    mock_flight = {
        'trips': [
            {
                'flight_nos': ['VZ123'],
                'cabin_class': 'A',
                'dep_date': '2024-01-15',
                'dep_time': '10:30',
                'fares': {'adult': {'base': 1500, 'tax': 300}},
            }
        ]
    }

    # 模拟预定参数
    mock_params = {
        'airline_code': 'VZ',
        'dep_airport_code': 'BKK',
        'arr_airport_code': 'CNX',
        'flight_no': 'VZ123',
        'currency_code': 'USD',
    }

    print(f"原始参数: {mock_params}")
    print(f"航班信息: 舱位={mock_flight['trips'][0]['cabin_class']}")

    # 构建任务数据用于规则判断
    task_data_for_rule = {
        'airline_code': mock_params['airline_code'],
        'dep_airport_code': mock_params['dep_airport_code'],
        'arr_airport_code': mock_params['arr_airport_code'],
        'cabin_class': mock_flight['trips'][0].get('cabin_class', ''),
        'currency_code': mock_params['currency_code'],
    }

    print(f"规则判断数据: {task_data_for_rule}")

    # 检查是否符合VZ境内线A舱条件
    should_use_thb = should_use_thb_for_vz_domestic(task_data_for_rule)
    should_requery = should_use_thb and mock_params['currency_code'] != 'THB'

    print(f"是否符合VZ境内线A舱条件: {should_use_thb}")
    print(f"是否需要用THB重新查询: {should_requery}")

    if should_requery:
        print("✓ 模拟: 调用 sv.back_index() 回到首页")
        print("✓ 模拟: 使用THB重新查询航班")
        mock_params['currency_code'] = 'THB'
        print(f"更新后参数: {mock_params}")
    else:
        print("✓ 模拟: 继续使用原币种")


def test_different_scenarios():
    """测试不同场景"""
    print("\n=== 测试不同场景 ===")

    scenarios = [
        {
            'name': '场景1: VZ境内线A舱USD -> THB',
            'params': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'currency_code': 'USD',
            },
            'flight': {'trips': [{'cabin_class': 'A'}]},
            'expected_requery': True,
        },
        {
            'name': '场景2: VZ境内线A舱已是THB',
            'params': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'currency_code': 'THB',
            },
            'flight': {'trips': [{'cabin_class': 'A'}]},
            'expected_requery': False,
        },
        {
            'name': '场景3: VJ航空境内线A舱',
            'params': {
                'airline_code': 'VJ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'currency_code': 'USD',
            },
            'flight': {'trips': [{'cabin_class': 'A'}]},
            'expected_requery': False,
        },
        {
            'name': '场景4: VZ国际线A舱',
            'params': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'SGN',
                'currency_code': 'USD',
            },
            'flight': {'trips': [{'cabin_class': 'A'}]},
            'expected_requery': False,
        },
    ]

    for scenario in scenarios:
        print(f"\n  {scenario['name']}")

        task_data = {
            'airline_code': scenario['params']['airline_code'],
            'dep_airport_code': scenario['params']['dep_airport_code'],
            'arr_airport_code': scenario['params']['arr_airport_code'],
            'cabin_class': scenario['flight']['trips'][0].get('cabin_class', ''),
            'currency_code': scenario['params']['currency_code'],
        }

        should_use_thb = should_use_thb_for_vz_domestic(task_data)
        should_requery = should_use_thb and scenario['params']['currency_code'] != 'THB'

        status = "✓" if should_requery == scenario['expected_requery'] else "✗"
        print(f"    {status} 是否重新查询: {should_requery} (期望: {scenario['expected_requery']})")


def main():
    """主函数"""
    print("VZ境内线A舱THB规则 - agent_service.py集成测试\n")

    try:
        test_vz_thb_rule_logic()
        simulate_agent_service_logic()
        test_different_scenarios()

        print("\n=== 测试完成 ===")
        print("如果所有测试都显示 ✓，说明agent_service.py中的VZ规则集成正常工作")

    except Exception as e:
        print(f"\n测试过程中出错: {e}")
        import traceback

        traceback.print_exc()


if __name__ == '__main__':
    main()
