#!/usr/bin/env python3
"""
测试VZ境内线A舱THB规则功能
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.helper_service import (
    is_thailand_domestic_route,
    should_use_thb_for_vz_domestic,
    apply_vz_domestic_thb_rule
)

def test_thailand_domestic_route():
    """测试泰国境内线判断"""
    print("=== 测试泰国境内线判断 ===")
    
    # 测试境内线
    test_cases = [
        ('BKK', 'CNX', True, '曼谷-清迈'),
        ('HKT', 'KBV', True, '普吉-甲米'),
        ('CEI', 'HKT', True, '清莱-普吉'),
        ('BKK', 'SGN', False, '曼谷-胡志明市（国际线）'),
        ('HAN', 'BKK', False, '河内-曼谷（国际线）'),
        ('PVG', 'NRT', False, '上海-东京（国际线）'),
    ]
    
    for dep, arr, expected, desc in test_cases:
        result = is_thailand_domestic_route(dep, arr)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {dep}-{arr}: {result} ({desc})")

def test_vz_thb_rules():
    """测试VZ境内线A舱THB规则"""
    print("\n=== 测试VZ境内线A舱THB规则 ===")
    
    test_cases = [
        {
            'name': 'VZ境内线A舱 - 应该使用THB',
            'task_data': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A',
                'currency_code': 'USD'
            },
            'should_use_thb': True,
            'expected_currency': 'THB'
        },
        {
            'name': 'VJ航空 - 不应该使用THB',
            'task_data': {
                'airline_code': 'VJ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A',
                'currency_code': 'USD'
            },
            'should_use_thb': False,
            'expected_currency': 'USD'
        },
        {
            'name': 'VZ国际线 - 不应该使用THB',
            'task_data': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'SGN',
                'cabin_class': 'A',
                'currency_code': 'USD'
            },
            'should_use_thb': False,
            'expected_currency': 'USD'
        },
        {
            'name': 'VZ境内线Y舱 - 不应该使用THB',
            'task_data': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'Y',
                'currency_code': 'USD'
            },
            'should_use_thb': False,
            'expected_currency': 'USD'
        },
        {
            'name': 'VZ境内线无舱位指定 - 应该使用THB',
            'task_data': {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'currency_code': 'USD'
            },
            'should_use_thb': True,
            'expected_currency': 'THB'
        }
    ]
    
    for case in test_cases:
        print(f"\n  测试: {case['name']}")
        
        # 测试规则判断
        should_use = should_use_thb_for_vz_domestic(case['task_data'])
        status1 = "✓" if should_use == case['should_use_thb'] else "✗"
        print(f"    {status1} 规则判断: {should_use} (期望: {case['should_use_thb']})")
        
        # 测试规则应用
        original_currency = case['task_data']['currency_code']
        result = apply_vz_domestic_thb_rule(case['task_data'])
        final_currency = result['currency_code']
        status2 = "✓" if final_currency == case['expected_currency'] else "✗"
        print(f"    {status2} 币种转换: {original_currency} → {final_currency} (期望: {case['expected_currency']})")
        
        # 验证原始数据未被修改
        if case['task_data']['currency_code'] == original_currency:
            print(f"    ✓ 原始数据未被修改")
        else:
            print(f"    ✗ 原始数据被意外修改")

def test_domestic_routes_from_config():
    """测试配置文件中的境内线"""
    print("\n=== 测试配置文件中的境内线 ===")
    
    from app.config import new_settings
    
    try:
        vz_rules = new_settings.get('vz_domestic_thb_rules')
        if vz_rules:
            domestic_routes = vz_rules.get('domestic_routes', [])
            print(f"配置文件中的境内线数量: {len(domestic_routes)}")
            
            # 验证每条境内线
            valid_count = 0
            for route in domestic_routes[:5]:  # 只测试前5条
                dep, arr = route.split('-')
                is_domestic = is_thailand_domestic_route(dep, arr)
                status = "✓" if is_domestic else "✗"
                print(f"  {status} {route}: {is_domestic}")
                if is_domestic:
                    valid_count += 1
            
            print(f"前5条线路验证通过: {valid_count}/5")
        else:
            print("未找到VZ规则配置")
    except Exception as e:
        print(f"测试配置文件时出错: {e}")

def main():
    """主函数"""
    print("VZ境内线A舱THB规则功能测试\n")
    
    try:
        test_thailand_domestic_route()
        test_vz_thb_rules()
        test_domestic_routes_from_config()
        
        print("\n=== 测试完成 ===")
        print("如果所有测试都显示 ✓，说明功能正常工作")
        
    except Exception as e:
        print(f"\n测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
