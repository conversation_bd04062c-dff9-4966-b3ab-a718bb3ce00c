#!/usr/bin/env python3
"""
分析提供的航线数据，识别泰国境内线
"""

# 提供的航线数据
PROVIDED_ROUTES = [
    "BKK,BOM",
    "BKK,CEI",
    "BKK,CNX",
    "BKK,DAD",
    "BKK,FUK",
    "BKK,HDY",
    "BKK,HGH",
    "BKK,HKT",
    "BKK,KBV",
    "BKK,KKC",
    "BKK,PKX",
    "BKK,PNH",
    "BKK,PQC",
    "BKK,PVG",
    "BKK,TPE",
    "BKK,UBP",
    "BKK,URT",
    "BKK,UTH",
    "BOM,BKK",
    "CEI,BKK",
    "CEI,HKT",
    "CNX,BKK",
    "CNX,HKT",
    "CNX,KIX",
    "DAD,BKK",
    "FUK,BKK",
    "HDY,BKK",
    "HGH,BKK",
    "HKT,BKK",
    "HKT,CNX",
    "KBV,BKK",
    "KIX,CNX",
    "KIX,TPE",
    "KKC,BKK",
    "PKX,BKK",
    "PNH,BKK",
    "PQC,BKK",
    "PVG,BKK",
    "TPE,BKK",
    "UBP,BKK",
    "URT,BKK",
    "UTH,BKK",
]

# 泰国机场代码（基于维基百科泰国机场列表）
THAILAND_AIRPORTS = {
    # 国际机场
    'BKK',
    'DMK',
    'BTZ',
    'CNX',
    'CEI',
    'HDY',
    'KKC',
    'USM',
    'KBV',
    'NST',
    'HKT',
    'UTP',
    'URT',
    'UTH',
    # 国内机场
    'BFV',
    'CJM',
    'HHQ',
    'LPT',
    'LOE',
    'HGN',
    'MAQ',
    'KOP',
    'NAK',
    'NNT',
    'NAW',
    'PHY',
    'PHS',
    'PRH',
    'UNN',
    'ROI',
    'SNO',
    'THS',
    'TST',
    'TDX',
    'UBP',
    # 军用/私人/特殊用途机场（仍属泰国境内）
    # QHI - Bang Phra Airport (春武里市) - 属于Thai Flying Club
    # KDT - Kamphaeng Saen Airport (佛统府) - 属于泰国皇家空军
    # KKM - Khok Kathiam Air Force Base (华富里) - 属于泰国皇家空军
    # TKH - Takhli Air Force Base (那空沙旺) - 属于泰国皇家空军
    # PYY - Pai Airport (夜丰颂府) - 属于机场管理局
    # PAN - Pattani Airport (北大年府) - 属于机场管理局
    # PHZ - Phi Phi Island Airport (甲米府披披群岛)
    # SGZ - Songkhla Airport (宋卡府) - 属于泰国皇家海军
    # PXR - Surin Airport (素林府) - 属于机场管理局
    # TKT - Tak Airport (达府) - 属于AOT
    # UTR - Uttaradit Airport (程逸府)
    'QHI',
    'KDT',
    'KKM',
    'TKH',
    'PYY',
    'PAN',
    'PHZ',
    'SGZ',
    'PXR',
    'TKT',
    'UTR',
}

# 非泰国机场的说明
NON_THAILAND_AIRPORTS = {
    'BOM': '印度孟买',
    'DAD': '越南岘港',
    'FUK': '日本福冈',
    'HGH': '中国杭州',
    'PKX': '中国北京大兴',
    'PNH': '柬埔寨金边',
    'PQC': '越南富国岛',
    'PVG': '中国上海浦东',
    'TPE': '台湾桃园',
    'KIX': '日本关西',
}


def analyze_routes():
    """分析航线数据"""
    print("=== 航线数据分析 ===\n")

    domestic_routes = []
    international_routes = []

    for route in PROVIDED_ROUTES:
        dep, arr = route.split(',')

        # 判断是否为泰国境内线
        if dep in THAILAND_AIRPORTS and arr in THAILAND_AIRPORTS:
            domestic_routes.append(f"{dep}-{arr}")
        else:
            international_routes.append(f"{dep}-{arr}")

    print(f"总航线数: {len(PROVIDED_ROUTES)}")
    print(f"泰国境内线数: {len(domestic_routes)}")
    print(f"国际航线数: {len(international_routes)}\n")

    print("=== 泰国境内线列表 ===")
    for route in sorted(domestic_routes):
        dep, arr = route.split('-')
        print(f"  {route}  # {get_airport_name(dep)}-{get_airport_name(arr)}")

    print(f"\n=== 国际航线列表 ===")
    for route in sorted(international_routes):
        dep, arr = route.split('-')
        dep_info = get_airport_info(dep)
        arr_info = get_airport_info(arr)
        print(f"  {route}  # {dep_info} → {arr_info}")

    print(f"\n=== 配置文件格式 ===")
    print("domestic_routes = [")
    for route in sorted(domestic_routes):
        dep, arr = route.split('-')
        print(f'    "{route}",  # {get_airport_name(dep)}-{get_airport_name(arr)}')
    print("]")


def get_airport_name(code):
    """获取机场中文名称"""
    airport_names = {
        'BKK': '曼谷素万那普',
        'CNX': '清迈',
        'HKT': '普吉',
        'KBV': '甲米',
        'UBP': '乌汶',
        'URT': '素叻他尼',
        'UTH': '乌隆他尼',
        'CEI': '清莱',
        'HDY': '合艾',
        'KKC': '孔敬',
    }
    return airport_names.get(code, code)


def get_airport_info(code):
    """获取机场信息"""
    if code in THAILAND_AIRPORTS:
        return f"泰国{get_airport_name(code)}"
    elif code in NON_THAILAND_AIRPORTS:
        return NON_THAILAND_AIRPORTS[code]
    else:
        return f"未知({code})"


if __name__ == '__main__':
    analyze_routes()
