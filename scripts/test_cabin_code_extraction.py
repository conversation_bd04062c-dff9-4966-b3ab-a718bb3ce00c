#!/usr/bin/env python3
"""
测试舱位代码提取逻辑
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.helper_service import should_use_thb_for_vz_domestic

def extract_cabin_code(flight):
    """
    提取舱位代码的第一个字符（模拟agent_service.py中的逻辑）
    """
    cabin_code = ''
    if flight['trips'][0].get('cabin_codes') and flight['trips'][0]['cabin_codes']:
        cabin_code = flight['trips'][0]['cabin_codes'][0][0]  # 取第一个舱位代码的第一个字符
    elif (flight['trips'][0].get('segments') and 
          flight['trips'][0]['segments'] and 
          flight['trips'][0]['segments'][0].get('cabin', {}).get('code')):
        cabin_code = flight['trips'][0]['segments'][0]['cabin']['code'][0]  # 取第一个字符
    
    return cabin_code

def test_cabin_code_extraction():
    """测试舱位代码提取"""
    print("=== 测试舱位代码提取 ===")
    
    # 测试数据：模拟真实的航班数据结构
    test_flights = [
        {
            'name': 'VZ130 - A1舱 (应该符合A舱条件)',
            'flight': {
                'trips': [{
                    'cabin_codes': ['A1'],
                    'segments': [{
                        'cabin': {
                            'cabin_class': 'Eco',
                            'code': 'A1',
                            'name': '经济舱'
                        }
                    }]
                }]
            },
            'expected_cabin': 'A',
            'should_match': True
        },
        {
            'name': 'VZ132 - W1舱 (不应该符合A舱条件)',
            'flight': {
                'trips': [{
                    'cabin_codes': ['W1'],
                    'segments': [{
                        'cabin': {
                            'cabin_class': 'Eco',
                            'code': 'W1',
                            'name': '经济舱'
                        }
                    }]
                }]
            },
            'expected_cabin': 'W',
            'should_match': False
        },
        {
            'name': 'VZ134 - A1舱 (应该符合A舱条件)',
            'flight': {
                'trips': [{
                    'cabin_codes': ['A1'],
                    'segments': [{
                        'cabin': {
                            'cabin_class': 'Eco',
                            'code': 'A1',
                            'name': '经济舱'
                        }
                    }]
                }]
            },
            'expected_cabin': 'A',
            'should_match': True
        },
        {
            'name': '只有segments数据的航班',
            'flight': {
                'trips': [{
                    'segments': [{
                        'cabin': {
                            'cabin_class': 'Eco',
                            'code': 'Y2',
                            'name': '经济舱'
                        }
                    }]
                }]
            },
            'expected_cabin': 'Y',
            'should_match': False
        },
        {
            'name': '空数据',
            'flight': {
                'trips': [{}]
            },
            'expected_cabin': '',
            'should_match': False
        }
    ]
    
    for test_case in test_flights:
        print(f"\n  测试: {test_case['name']}")
        
        # 提取舱位代码
        cabin_code = extract_cabin_code(test_case['flight'])
        status1 = "✓" if cabin_code == test_case['expected_cabin'] else "✗"
        print(f"    {status1} 舱位提取: '{cabin_code}' (期望: '{test_case['expected_cabin']}')")
        
        # 测试VZ规则判断
        task_data = {
            'airline_code': 'VZ',
            'dep_airport_code': 'BKK',
            'arr_airport_code': 'CEI',
            'cabin_class': cabin_code,
            'currency_code': 'USD'
        }
        
        should_use_thb = should_use_thb_for_vz_domestic(task_data)
        status2 = "✓" if should_use_thb == test_case['should_match'] else "✗"
        print(f"    {status2} VZ规则判断: {should_use_thb} (期望: {test_case['should_match']})")

def test_real_log_data():
    """测试真实日志数据"""
    print("\n=== 测试真实日志数据 ===")
    
    # 从用户提供的日志中提取的真实数据
    real_flights = [
        {
            'name': 'VZ130 - A1舱',
            'flight_no': 'VZ130',
            'cabin_codes': ['A1'],
            'expected_should_use_thb': True
        },
        {
            'name': 'VZ132 - W1舱',
            'flight_no': 'VZ132', 
            'cabin_codes': ['W1'],
            'expected_should_use_thb': False
        },
        {
            'name': 'VZ134 - A1舱',
            'flight_no': 'VZ134',
            'cabin_codes': ['A1'],
            'expected_should_use_thb': True
        }
    ]
    
    for flight_data in real_flights:
        print(f"\n  测试: {flight_data['name']}")
        
        # 构建航班数据
        flight = {
            'trips': [{
                'cabin_codes': flight_data['cabin_codes'],
                'flight_nos': [flight_data['flight_no']]
            }]
        }
        
        # 提取舱位代码
        cabin_code = extract_cabin_code(flight)
        print(f"    舱位代码: {flight_data['cabin_codes'][0]} -> 第一个字符: '{cabin_code}'")
        
        # 测试VZ规则判断
        task_data = {
            'airline_code': 'VZ',
            'dep_airport_code': 'BKK',
            'arr_airport_code': 'CEI',
            'cabin_class': cabin_code,
            'currency_code': 'USD'
        }
        
        should_use_thb = should_use_thb_for_vz_domestic(task_data)
        status = "✓" if should_use_thb == flight_data['expected_should_use_thb'] else "✗"
        print(f"    {status} VZ规则判断: {should_use_thb} (期望: {flight_data['expected_should_use_thb']})")
        
        if should_use_thb != flight_data['expected_should_use_thb']:
            print(f"    ❌ 错误: {flight_data['flight_no']} 舱位 {cabin_code} 的判断结果不正确")

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    edge_cases = [
        {
            'name': '空字符串舱位',
            'cabin_class': '',
            'expected': False
        },
        {
            'name': '单字符A舱',
            'cabin_class': 'A',
            'expected': True
        },
        {
            'name': '小写a舱',
            'cabin_class': 'a',
            'expected': True  # 应该不区分大小写
        },
        {
            'name': '其他舱位B',
            'cabin_class': 'B',
            'expected': False
        },
        {
            'name': '其他舱位Y',
            'cabin_class': 'Y',
            'expected': False
        }
    ]
    
    for case in edge_cases:
        print(f"\n  测试: {case['name']}")
        
        task_data = {
            'airline_code': 'VZ',
            'dep_airport_code': 'BKK',
            'arr_airport_code': 'CEI',
            'cabin_class': case['cabin_class'],
            'currency_code': 'USD'
        }
        
        should_use_thb = should_use_thb_for_vz_domestic(task_data)
        status = "✓" if should_use_thb == case['expected'] else "✗"
        print(f"    {status} 舱位 '{case['cabin_class']}' -> {should_use_thb} (期望: {case['expected']})")

def main():
    """主函数"""
    print("VZ境内线A舱THB规则 - 舱位代码提取测试\n")
    
    try:
        test_cabin_code_extraction()
        test_real_log_data()
        test_edge_cases()
        
        print("\n=== 测试完成 ===")
        print("如果所有测试都显示 ✓，说明舱位代码提取和判断逻辑正确")
        
    except Exception as e:
        print(f"\n测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
