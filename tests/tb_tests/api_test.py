import base64
from datetime import datetime, timed<PERSON>ta
import os

from loguru import logger
import or<PERSON><PERSON>
import urllib
from app.config import settings
from app.tb.utils import CipherUtils
import base64
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad  # 导入 pad 和 unpad 方法
import pytest

import requests

from commons.consts.api_codes import ApiCodes


def test_tb_all():
    
    host = 'http://*************:8087'
    #host = 'http://************:30000'
    #product = 'baggage'
    #product = 'gold'
    product = 'normal'
    url_path_suffix = f'api/v1/tb'

    search_url = f'{host}/{url_path_suffix}/{product}/search'
    search_data = {
        "cid": "huiyoushanglv",
        "tripType": 1,
        "stressTest": False,
        "fromCity": "BKK",
        "toCity": "CEI",
        "fromDate": "********",
        "adultNumber": 1,
        "childNumber": 0,
        "infantNumber": 0,
    }
    search_resp = requests.post(search_url, json=search_data)
    logger.debug(f'search_resp: {search_resp.text}')
    search_result = search_resp.json()

    assert search_result['status'] == 0
    assert search_result.get('routings')

    flight_no = None
    flight_no = 'VZ130'
    # fare_type = 'normal'
    # fare_type = 'fpo'
    if flight_no:
        routing = [r for r in search_result['routings'] if flight_no in r['fromSegments'][0]['flightNumber']][0]
    else:
        routing = search_result['routings'][0]

    logger.debug(f'routing: {routing}')
    # assert False

    verify_url = f'{host}/{url_path_suffix}/{product}/verify'
    verify_data = {"cid": "huiyoushanglv", "tripType": 1, "stressTest": False, "routing": routing}
    logger.debug(f'verify_data: {verify_data}')
    verify_resp = requests.post(verify_url, json=verify_data)
    logger.debug(f'verify_resp: {verify_resp.text}')
    verify_result = verify_resp.json()

    assert verify_result['status'] == 0
    assert verify_result.get('sessionId')

    session_id = verify_result['sessionId']

    order_url = f'{host}/{url_path_suffix}/{product}/order/create'
    order_info = {
        "cid": "huiyoushanglv",
        "tripType": 1,
        "sessionId": session_id,
        "routing": routing,
        "passengers": [
            {
                "name": "yao/feng",
                "ageType": 0,
                "birthday": "********",
                "gender": "M",
                "cardNum": "E8796543",
                "cardType": "PP",
                "cardIssuePlace": "CN",
                "cardExpired": "********",
                "nationality": "CN",
            },
            #{
            #    "name": "yao/feng/fang",
            #    "ageType": 0,
            #    "birthday": "********",
            #    "gender": "M",
            #    "cardNum": "E8796573",
            #    "cardType": "PP",
            #    "cardIssuePlace": "CN",
            #    "cardExpired": "********",
            #    "nationality": "CN",
            #},
        ],
        "contact": {"name": "yao/feng", "mobile": "***********"},
        "passengerAuxes": [],
    }
    passenger_auxes = [
        {
            "bookNum": 1,
            "name": p['name'],
            "productItem": {
                "auxFareSource": 1,
                "baggage": {"isAllWeight": True, "pc": 1, "weight": 20},
                "onlinePrice": 16000,
                "outerId": "****************",
                "productName": "20KG",
                "productType": 4,
                "saleType": 1,
            },
            "segment": {
                "arrAirport": routing['fromSegments'][0]['arrAirport'],
                "cabin": "G",
                "depAirport": routing['fromSegments'][0]['depAirport'],
                "depTime": routing['fromSegments'][0]['depTime'],
                "flightNumber": routing['fromSegments'][0]['flightNumber'],
            },
        }
        for p in order_info['passengers'][:1]
    ]
    #passenger_auxes = []
    if passenger_auxes:
        order_info['passengerAuxes'] = passenger_auxes

    order_data = orjson.dumps(order_info).decode('utf-8')

    headers = {'Content-Type': 'text/plain'}
    logger.debug(f'order_data: {order_data}')
    order_data_str = CipherUtils.aes_encrypt(key=settings.TB_AES_KEY, plaintext=order_data)
    logger.debug(f'order_data_str: {order_data_str}')
    order_resp = requests.post(order_url, data=order_data_str, headers=headers)
    order_result_str = order_resp.text
    order_result = orjson.loads(CipherUtils.aes_decrypt(key=settings.TB_AES_KEY, ciphertext=order_result_str))
    logger.debug(f'order_resp: {order_result}')

    assert order_result['status'] == 0
    assert order_result.get('orderNo')

    order_no = order_result['orderNo']

    pay_url = f'{host}/{url_path_suffix}/{product}/order/pay_verify'
    pay_data = orjson.dumps(
        {
            "cid": "huiyoushanglv",
            "tripType": 1,
            "sessionId": session_id,
            "orderNo": order_no,
            "pnrCode": order_result['pnrCode'],
            "routing": routing,
        }
    ).decode('utf-8')

    pay_data_str = CipherUtils.aes_encrypt(key=settings.TB_AES_KEY, plaintext=pay_data)
    logger.debug(f'pay_data_str: {pay_data_str}')
    pay_resp = requests.post(pay_url, data=pay_data_str, headers=headers)
    pay_result_str = pay_resp.text
    pay_result = orjson.loads(CipherUtils.aes_decrypt(key=settings.TB_AES_KEY, ciphertext=pay_result_str))
    logger.debug(f'pay_resp: {pay_result}')

    assert pay_result['status'] == 0
    assert pay_result.get('sessionId')
    assert pay_result.get('orderNo')
