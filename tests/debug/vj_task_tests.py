from loguru import logger
import orj<PERSON>
import pytest

from app import vj_tasks
from app.vj_tasks import do_search
from app.clients.vj_client import VJClient


@pytest.fixture
def vj_search():
    return VJClient()


def test_search():
    task_data = {
        "unique_id": "f97957f2c02fb137a87d80de63f8444f",
        "site_code": "vietjetair",
        "site_type": "airline",
        "dep_airport_code": "PVG",
        "arr_airport_code": "HAN",
        "dep_date": "2025-05-15",
        "return_date": "",
        "trip_type": "ow",
        "status": "pending",
        "airline_code": "VJ",
        "schedule_id": 436,
        "fetch_rule_id": 18,
        "expire_seconds": 600,
        "task_key": "VJ-PVG-HAN-2025-05-15",
        "worker_id": 0,
        "create_time": "2025-05-09T15:53:53",
        "expire_time": "",
    }

    result = do_search(task_data=task_data, airline_code=task_data['airline_code'])
    logger.debug(result)


def test_send_task():
    task_params = {
        'unique_id': '0a33332d4191b5c14dd0452ac17884bb',
        'site_code': 'vietjetair',
        'site_type': 'airline',
        'dep_airport_code': 'BKK',
        'arr_airport_code': 'CEI',
        'dep_date': '2024-12-26',
        'return_date': '',
        'trip_type': 'ow',
        'status': 'pending',
        'airline_code': 'VZ',
        'schedule_id': 133,
        'fetch_rule_id': 7,
        'expire_seconds': 600,
        'task_key': 'VZ-BKK-CEI-2024-12-26',
        'currency_code': 'THB',
        'create_time': '2024-12-24T17:27:33',
    }
    task = vj_tasks.celery_app.signature("vz_search_task", args=(task_params,), queue='vz_search_queue')
    callback = vj_tasks.celery_app.signature("vz_search_callback_task", queue='vz_search_queue')

    # 调度任务
    task.apply_async(link=callback)


def test_verify_book():

    task_data = {
        "callback_url": "http://*************:9528/api/v1/flight_fare/crawler/callback/verify/book/result",
        "start_task_url": None,
        "task_type": None,
        "order_no": "20250523185419616",
        "mock_pnr": "LEVORH",
        # 可切换dlx航线
        # "airline_code": "VJ",
        # "dep_airport_code": "SGN",
        # "arr_airport_code": "SYD",
        # "dep_date": "2025-07-31",
        # "flight_no": "VJ085",
        # 不可切换dlx航线
        # "airline_code": "VJ",
        # "dep_airport_code": "HAN",
        # "arr_airport_code": "SGN",
        # "dep_date": "2025-07-28",
        # "flight_no": "VJ1199",
        # 测试越南境内线
        "airline_code": "VZ",
        "dep_airport_code": "BKK",
        "arr_airport_code": "CEI",
        "dep_date": "2025-06-09",
        "flight_no": "VZ132",
        "adult": 2,
        "child": 0,
        "infant": 0,
        "currency_code": "CNY",
        # "src_adult_base": 6444.68,
        # "src_adult_tax": 70622.97,
        "src_adult_base": 6444.68,
        "src_adult_tax": 113034,
        "passengers": [
            {
                "name": "MENG/YUFANG",
                "last_name": "MENG",
                "first_name": "YUFANG",
                "birthday": "1987-03-03",
                "sex": "male",
                "passenger_type": "adult",
                "country": "CN",
                "card_no": "*********",
                "card_valid_date": "2035-05-19",
                "card_country": "CN",
                "baggages": [
                    {
                        "name": "MENG/YUFANG",
                        "dep_airport_code": "CAN",
                        "arr_airport_code": "HAN",
                        "dep_date": "2025-05-28",
                        "flight_no": "VJ7527",
                        "dep_time": "03:05",
                        "outer_id": "",
                        "aux_type": "baggage",
                        "weight": 30.0,
                        "size": 0.0,
                        "count": 1,
                        "price": 0.0,
                        "desc": "{\"count\":1,\"weight\":20,\"all_weight\":20}",
                    }
                ],
            },
            {
                "name": "SU/LIN",
                "last_name": "SU",
                "first_name": "LIN",
                "birthday": "1975-11-26",
                "sex": "male",
                "passenger_type": "adult",
                "country": "CN",
                "card_no": "EP4660034",
                "card_valid_date": "2035-03-06",
                "card_country": "CN",
                "baggages": [
                    {
                        "name": "SU/LIN",
                        "dep_airport_code": "CAN",
                        "arr_airport_code": "HAN",
                        "dep_date": "2025-05-28",
                        "flight_no": "VJ7527",
                        "dep_time": "03:05",
                        "outer_id": "",
                        "aux_type": "baggage",
                        "weight": 30.0,
                        "size": 0.0,
                        "count": 1,
                        "price": 0.0,
                        "desc": "{\"count\":1,\"weight\":20,\"all_weight\":20}",
                    }
                ],
            },
            # {
            #     "name": "LU/XIAOYAN",
            #     "last_name": "LU",
            #     "first_name": "XIAOYAN",
            #     "birthday": "1984-01-06",
            #     "sex": "female",
            #     "passenger_type": "adult",
            #     "country": "CN",
            #     "card_no": "EM8365195",
            #     "card_valid_date": "2034-06-20",
            #     "card_country": "CN",
            #     "baggages": [
            #         {
            #             "name": "LU/XIAOYAN",
            #             "dep_airport_code": "CAN",
            #             "arr_airport_code": "HAN",
            #             "dep_date": "2025-05-28",
            #             "flight_no": "VJ7527",
            #             "dep_time": "03:05",
            #             "outer_id": "",
            #             "aux_type": "baggage",
            #             "weight": 20.0,
            #             "size": 0.0,
            #             "count": 1,
            #             "price": 0.0,
            #             "desc": "{\"count\":1,\"weight\":20,\"all_weight\":20}",
            #         }
            #     ],
            # },
        ],
        "contact": {
            "name": "PHAM/THIHONGNHUNG",
            "last_name": "PHAM",
            "first_name": "THIHONGNHUNG",
            "mobile": "13818432182",
            "email": "<EMAIL>",
            "country": "CN",
            "area_code": "+86",
            "city": "Beijing",
            "post_code": "100000",
            "address": "Room 208, Building 3, Jinyuchi Community, Dongcheng District, Beijing",
        },
        "base_float": 0,
        "auto_try_times": 1,
        "dep_time": None,
        "dep_diff_minutes": None,
        "unique_id": "ffe97b2b-e08e-4cb4-8315-f218ab30fea9",
        "host": "agents.vietjetair.com",
        "proxy": "http://***********:12323",
    }

    result = vj_tasks.do_verify_book(task_data, airline_code='VJ')
    logger.debug(result)
