"""
VZ境内线A舱THB规则测试
"""
import pytest
from unittest.mock import patch, MagicMock
from app.services.helper_service import (
    is_thailand_domestic_route,
    should_use_thb_for_vz_domestic,
    apply_vz_domestic_thb_rule
)


class TestVZDomesticTHBRules:
    """VZ境内线THB规则测试类"""

    @pytest.fixture
    def mock_vz_rules(self):
        """模拟VZ规则配置"""
        return {
            'enabled': True,
            'airline_codes': ['VZ'],
            'cabin_classes': ['A'],
            'force_currency': 'THB',
            'thailand_airports': [
                'BKK', 'CNX', 'HKT', 'KBV', 'UBP', 'URT', 'UTH',
                'DMK', 'CEI', 'HDY', 'USM', 'NST', 'UTP'
            ],
            'domestic_routes': [
                'BKK-CNX', 'BKK-HKT', 'BKK-KBV', 'BKK-UBP', 'BKK-URT', 'BKK-UTH',
                'CNX-BKK', 'CNX-HKT', 'HKT-BKK', 'HKT-CNX', 'KBV-BKK',
                'UBP-BKK', 'URT-BKK', 'UTH-BKK'
            ]
        }

    def test_is_thailand_domestic_route_true(self, mock_vz_rules):
        """测试泰国境内线判断 - 正确情况"""
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            # 测试泰国境内线
            assert is_thailand_domestic_route('BKK', 'CNX') == True
            assert is_thailand_domestic_route('HKT', 'KBV') == True
            assert is_thailand_domestic_route('bkk', 'cnx') == True  # 测试大小写

    def test_is_thailand_domestic_route_false(self, mock_vz_rules):
        """测试泰国境内线判断 - 非境内线"""
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            # 测试非泰国境内线
            assert is_thailand_domestic_route('BKK', 'SGN') == False  # 泰国-越南
            assert is_thailand_domestic_route('HAN', 'BKK') == False  # 越南-泰国
            assert is_thailand_domestic_route('PVG', 'NRT') == False  # 中国-日本

    def test_should_use_thb_for_vz_domestic_true(self, mock_vz_rules):
        """测试VZ境内线A舱THB规则 - 应该使用THB"""
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            task_data = {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A'
            }
            
            assert should_use_thb_for_vz_domestic(task_data) == True

    def test_should_use_thb_for_vz_domestic_false_airline(self, mock_vz_rules):
        """测试VZ境内线A舱THB规则 - 非VZ航空"""
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            task_data = {
                'airline_code': 'VJ',  # 非VZ
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A'
            }
            
            assert should_use_thb_for_vz_domestic(task_data) == False

    def test_should_use_thb_for_vz_domestic_false_route(self, mock_vz_rules):
        """测试VZ境内线A舱THB规则 - 非境内线"""
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            task_data = {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'SGN',  # 非泰国机场
                'cabin_class': 'A'
            }
            
            assert should_use_thb_for_vz_domestic(task_data) == False

    def test_should_use_thb_for_vz_domestic_false_cabin(self, mock_vz_rules):
        """测试VZ境内线A舱THB规则 - 非A舱"""
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            task_data = {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'Y'  # 非A舱
            }
            
            assert should_use_thb_for_vz_domestic(task_data) == False

    def test_apply_vz_domestic_thb_rule_applied(self, mock_vz_rules):
        """测试应用VZ境内线THB规则 - 规则被应用"""
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            task_data = {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A',
                'currency_code': 'USD'
            }
            
            result = apply_vz_domestic_thb_rule(task_data)
            
            # 验证币种被改为THB
            assert result['currency_code'] == 'THB'
            # 验证原始数据没有被修改
            assert task_data['currency_code'] == 'USD'

    def test_apply_vz_domestic_thb_rule_not_applied(self, mock_vz_rules):
        """测试应用VZ境内线THB规则 - 规则未被应用"""
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            task_data = {
                'airline_code': 'VJ',  # 非VZ
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A',
                'currency_code': 'USD'
            }
            
            result = apply_vz_domestic_thb_rule(task_data)
            
            # 验证币种没有被改变
            assert result['currency_code'] == 'USD'
            # 验证返回的是同一个对象
            assert result is task_data

    def test_disabled_rules(self, mock_vz_rules):
        """测试禁用规则的情况"""
        mock_vz_rules['enabled'] = False
        
        with patch('app.services.helper_service.new_settings') as mock_settings:
            mock_settings.get.return_value = mock_vz_rules
            
            task_data = {
                'airline_code': 'VZ',
                'dep_airport_code': 'BKK',
                'arr_airport_code': 'CNX',
                'cabin_class': 'A',
                'currency_code': 'USD'
            }
            
            # 规则被禁用，应该不应用THB
            assert should_use_thb_for_vz_domestic(task_data) == False
            
            result = apply_vz_domestic_thb_rule(task_data)
            assert result['currency_code'] == 'USD'


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
