<template>
  <div class="app-container">
    <!-- 搜索区 -->
    <el-form
      ref="topForm"
      :inline="true"
      style="vertical-align: middle"
      size="small"
    >
      <el-form-item label="订单号">
        <el-input v-model="searchForm.conditions.order_no"></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchForm.conditions.status" style="width: 100px">
          <el-option label="全部" :value="undefined"></el-option>
          <el-option
            v-for="item in orderStatus.filter((item) => item.code < 0)"
            :key="item.code"
            :label="item.value"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button
          :loading="loading"
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
          >查询</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 查询结果：表格 -->
    <el-table
      ref="airlineTable"
      id="airlineTable"
      v-loading="loading"
      fit
      highlight-current-row
      :height="tableHeight"
      :data="orderSearchResult"
    >
      <el-table-column label="订单号" prop="order_no" min-width="180px">
        <template slot-scope="scope">
          <view-order-button
            style="display: inline-block"
            :currRow="scope.row"
            @refreshData="handleSearch"
          ></view-order-button>
          <copy-to-clipboard-btn
            :textToCopy="scope.row.order_no"
          ></copy-to-clipboard-btn>
        </template>
      </el-table-column>
      <el-table-column label="假PNR" prop="mock_pnr" min-width="120px">
        <template slot-scope="scope">
          {{ scope.row.mock_pnr || "--" }}
          <copy-to-clipboard-btn
            v-if="scope.row.mock_pnr"
            :textToCopy="scope.row.mock_pnr"
          ></copy-to-clipboard-btn>
        </template>
      </el-table-column>
      <el-table-column label="PNR" prop="pnr" min-width="160px">
        <template slot-scope="scope">
          {{ scope.row.pnr || "--" }}
          <copy-to-clipboard-btn
            v-if="scope.row.pnr"
            :textToCopy="scope.row.pnr"
          ></copy-to-clipboard-btn>
          <br />
          {{ scope.row.pnr_expire_time || "" }}

          <el-tag
            v-if="
              pnrIsExpired(scope.row.pnr_expire_time) &&
              scope.row.status < 130 &&
              scope.row.pnr
            "
            type="danger"
          >
            已过期
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="航班号" prop="flight_nos"> </el-table-column>
      <el-table-column label="FTD" prop="" min-width="100px">
        <template slot-scope="scope">
          {{ scope.row.dep_airport_code }}-{{ scope.row.arr_airport_code
          }}<br />{{ scope.row.dep_date }}
        </template>
      </el-table-column>
      <el-table-column label="A/C/I" prop="" min-width="120px">
        <template slot-scope="scope">
          {{ scope.row.adult_num }}/{{ scope.row.child_num }}/{{
            scope.row.infant_num
          }}
        </template>
      </el-table-column>
      <el-table-column label="总价CNY" prop="total_price" min-width="120px">
        <template slot-scope="scope">
          {{ scope.row.total_price }}
        </template>
      </el-table-column>
      <el-table-column
        label="过期时间"
        prop="expire_time"
        min-width="160px"
        sortable
      >
        <template slot-scope="scope">
          <span :style="{ color: timeColor(scope.row.expire_time) }">
            {{ scope.row.expire_time }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="状态" prop="status" min-width="180px">
        <template slot-scope="scope">
          {{ scope.row.status | statusDesc }}
        </template>
      </el-table-column>
      <el-table-column label="错误信息" prop="" min-width="160px">
        <template slot-scope="scope">
          {{ scope.row.error_message }}（{{ scope.row.error_code }}）
        </template>
      </el-table-column>
      <el-table-column
        label="操作员"
        prop="locker_name"
        fixed="right"
        min-width="100px"
      >
        <template slot-scope="scope">
          <el-form label-width="0px" style="margin: 2px" size="mini" inline>
            <el-form-item style="margin: 2px">
              <locker-button
                :currRow="scope.row"
                @refreshData="handleSearch"
              ></locker-button>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" min-width="150px">
        <template slot-scope="scope">
          <operate-form size="mini">
            <template slot="buttons">
              <!-- 预占座 -->
              <el-form-item>
                <verify-book-button
                  v-if="enableVerifyBook(scope.row)"
                  type="warning"
                  :currRow="scope.row"
                  :disabled="scope.row.locker_id !== user.id"
                  :button-text="'重试预占座'"
                  @refreshData="handleSearch"
                ></verify-book-button>
              </el-form-item>

              <!-- 支付 -->
              <el-form-item>
                <confirm-pay-button
                  v-if="enableConfirmPay(scope.row)"
                  type="danger"
                  :disabled="scope.row.locker_id !== user.id"
                  :currRow="scope.row"
                  :button-text="'重试支付'"
                  @refreshData="handleSearch"
                ></confirm-pay-button>
              </el-form-item>

              <!-- 出票 -->
              <el-form-item>
                <auto-book-button
                  v-if="enableAutoBook(scope.row)"
                  type="primary"
                  :currRow="scope.row"
                  :disabled="scope.row.locker_id !== user.id"
                  :button-text="'重试出票'"
                  @refreshData="handleSearch"
                ></auto-book-button>
              </el-form-item>
              <!-- 回填票号 -->
              <el-form-item>
                <issue-ticket-button
                  v-if="enableIssueTicket(scope.row)"
                  type="primary"
                  :currRow="scope.row"
                  :button-text="'票号回填'"
                  @refreshData="handleSearch"
                ></issue-ticket-button>
              </el-form-item>
              <!-- 取消订单 -->
              <el-form-item>
                <cancel-ticket-button
                  v-if="enableCancel(scope.row)"
                  :currRow="scope.row"
                  :disabled="scope.row.locker_id !== user.id"
                  @refresh="handleSearch"
                ></cancel-ticket-button>
              </el-form-item>

              <!-- <el-form-item>
                <re-do-button
                  :currRow="scope.row"
                  @refresh="handleSearch"
                ></re-do-button>
              </el-form-item>
              <el-form-item
                v-if="scope.row.status >= -160 && scope.row.status < 160"
              >
                <manual-book-button
                  :currRow="scope.row"
                  @manualTicketConfirm="handleSearch"
                >
                </manual-book-button>
              </el-form-item>
              <el-form-item v-if="scope.row.status < 160">
                <no-ticket-button
                  :currRow="scope.row"
                  @refresh="handleSearch"
                ></no-ticket-button>
              </el-form-item> -->
            </template>
          </operate-form>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      ref="pagination"
      class="pagination-center"
      layout="total, sizes, prev, pager, next, jumper"
      :current-page.sync="currPage"
      :page-sizes="paginationParams.page_sizes"
      :page-size.sync="paginationParams.size"
      :total="paginationParams.total"
      @size-change="
        (val) => {
          this.paginationParams.size = val;
          this.handleSearch();
        }
      "
      @current-change="
        (val) => {
          this.paginationParams.page = val;
          this.handleSearch();
        }
      "
    >
    </el-pagination>
    <!-- 抽屉栏 -->
  </div>
</template>

<script>
import allStatus from "@/consts/allStatus.js";
import moment from "moment";
import { mapGetters } from "vuex";
// 引入指令
import permission from "@/directive/permission/index.js"; // 权限判断指令
import CopyToClipboardBtn from "@/views/components/CopyToClipboardBtn.vue";
import OperateForm from "@/views/components/OperateForm.vue";
import AutoBookButton from "./components/OrderButtons/AutoBookButton.vue";
import CancelTicketButton from "./components/OrderButtons/CancelTicketButton.vue";
import ConfirmPayButton from "./components/OrderButtons/ConfirmPayButton.vue";
import IssueTicketButton from "./components/OrderButtons/IssueTicketButton.vue";
import LockerButton from "./components/OrderButtons/LockerButton.vue";
import ManualBookButton from "./components/OrderButtons/ManualBookButton.vue";
import NoTicketButton from "./components/OrderButtons/NoTicketButton.vue";
import ReDoButton from "./components/OrderButtons/ReDoButton.vue";
import VerifyBookButton from "./components/OrderButtons/VerifyBookButton.vue";
import ViewOrderButton from "./components/OrderButtons/ViewOrderButton.vue";

export default {
  components: {
    CopyToClipboardBtn,
    LockerButton,
    NoTicketButton,
    OperateForm,
    ManualBookButton,
    ViewOrderButton,
    ReDoButton,
    AutoBookButton,
    ConfirmPayButton,
    VerifyBookButton,
    IssueTicketButton,
    CancelTicketButton,
  },
  // 注册指令
  directives: { permission },
  data() {
    return {
      loading: false,
      orderSearchResult: [],
      adminRoleList: [],
      searchForm: {
        conditions: {
          order_no: undefined,
          status: undefined,
        },
        // order_by: {
        //   prop: "id",
        //   order: "descending",
        // },
      },

      paginationParams: this.$myUtil.pagination.getDefultParams(50),
      currPage: 1,
      //   optionTitle: "",
      //   drawerVisible: false,
      //   currRow: {},
      //   editable: false,
      orderStatus: allStatus.orderStatus,
    };
  },
  filters: {
    statusDesc: function (status) {
      return allStatus.orderStatusDesc(status);
    },
  },
  computed: {
    ...mapGetters(["user"]),
    tableHeight() {
      let height = document.body.clientHeight;
      if (this.$refs.topForm) {
        height = height - this.$refs.topForm.$el.offsetHeight * 2;
        height = height - this.$refs.topForm.$el.offsetTop * 2;
      }
      // console.debug(this.$refs)
      if (this.$refs.pagination) {
        height = height - this.$refs.pagination.$el.offsetHeight;
      }
      if (!this.$refs.topForm) {
        height = height * 0.79;
      }
      if (height < 500) {
        height = 500;
      }
      // console.debug(height)
      return height;
    },
    timeColor() {
      return function (timeStr) {
        const time = moment(timeStr);
        if (time.isAfter(moment().subtract(60, "minutes"))) {
          return "orange";
        }
        if (
          time.isAfter(moment().subtract(30, "minutes")) ||
          time.isBefore(moment().add(1, "seconds"))
        ) {
          return "red";
        }
        return;
      };
    },
    pnrIsExpired() {
      return function (pnr_expire_time) {
        if (!pnr_expire_time) {
          return true;
        }
        return moment(pnr_expire_time).isBefore(moment());
      };
    },
    enableAutoBook() {
      return function (row) {
        return (
          [-130, -110, -100, 100].includes(row.status) &&
          (!row.pnr || this.pnrIsExpired(row.pnr_expire_time))
        );
      };
    },
    enableConfirmPay() {
      return function (row) {
        return row.status >= -106 && row.status < 130 && row.pnr;
      };
    },
    enableVerifyBook() {
      return function (row) {
        return (
          row.status >= -103 &&
          row.status < 103 &&
          (!row.pnr || this.pnrIsExpired(row.pnr_expire_time))
        );
      };
    },
    enableIssueTicket() {
      return function (row) {
        return [-200, 130].includes(row.status) && row.pnr;
      };
    },
    enableCancel() {
      return function (row) {
        return row.status === -130;
      };
    },
  },

  created() {
    this.handleSearch();
    window.onresize = () => {
      this.$refs.airlineTable.doLayout();
    };
  },
  methods: {
    handleSearch() {
      const that = this;
      this.loading = true;
      this.$myApi.flightOrder
        .exceptSearch(this.searchForm)
        .then((res) => {
          console.debug(res);
          this.orderSearchResult = res.data.rows;
          this.paginationParams.total = res.data.total;
          setTimeout(() => {
            that.$refs.airlineTable.doLayout();
          }, 2000);
        })
        .catch((err) => {
          this.orderSearchResult = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss">
.dashboard {
  &-container {
    margin: 30px;
  }

  &-text {
    font-size: 30px;
    line-height: 46px;
  }
}

.el-table-filter {
  max-height: 500px;
  overflow: auto;
}
</style>
