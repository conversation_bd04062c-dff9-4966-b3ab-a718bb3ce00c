<template>
  <div>
    <el-button
      size="mini"
      type="danger"
      :disabled="currRow.locker_id !== user.id"
      :loading="loading"
      @click="handleOpen"
    >
      手工关单
    </el-button>

    <!-- 抽屉栏 -->
    <el-drawer
      size="62%"
      :title="
        '手工关单：' +
        (this.orderRow.locker_name ? `${this.orderRow.locker_name} 锁定中` : '')
      "
      destroy-on-close
      append-to-body
      :visible.sync="drawerVisible"
      :modal-append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
    >
      <el-divider content-position="left">订单信息</el-divider>
      <order-base-info
        v-if="orderRow.order_no"
        ref="orderBaseInfo"
        style="margin: 0px 30px"
        :orderRow="orderRow"
        :loading="loading"
      ></order-base-info>
      <el-divider content-position="left">操作</el-divider>
      <el-form
        ref="form"
        size="small"
        style="margin: 0px 30px"
        inline
        :rules="formRules"
        :model="form"
      >
        <el-row type="flex" justify="center" align="middle">
          <el-col :span="24">
            <el-form-item label="关单原因" prop="no_ticket_reason">
              <el-select
                v-model="form.no_ticket_reason"
                placeholder="请选择关单原因"
                style="width: 100%"
                filterable
              >
                <el-option
                  label="起飞时间变更，客人主动取消"
                  value="起飞时间变更，客人主动取消"
                ></el-option>
                <el-option label="航司原因" value="航司原因"></el-option>
                <el-option label="系统原因" value="系统原因"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" align="middle">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input type="textarea" v-model="form.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" justify="center" align="middle">
          <el-col :span="24">
            <el-form-item>
              <el-button @click="drawerVisible = false"> 取 消 </el-button>
              <el-button
                type="primary"
                :loading="loading"
                @click="cancelTicket"
              >
                确 定
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-drawer>
  </div>
</template>

<script>
// 引入组件
import { mapGetters } from "vuex";
// 引入指令
import permission from "@/directive/permission/index.js"; // 权限判断指令
import OrderBaseInfo from "../OrderBaseInfo.vue";
//
export default {
  // 注册组件
  components: { OrderBaseInfo },
  // 注册指令
  directives: { permission },
  //   当前组件属性
  name: "CancelTicketButton",

  props: {
    currRow: {
      type: Object,
      required: true,
    },
  },
  model: {
    prop: "currRow",
    event: "input-change",
  },
  watch: {
    // 监听
    currRow: {
      // 深度监听
      deep: true,
      // 初始化时也进行监听
      immediate: true,
      handler: function (newVal, oldVal) {
        // this.orderRow = newVal;
      },
    },
  },
  filters: {
    // 过滤器
  },

  data() {
    return {
      loading: false,
      orderRow: {},
      form: {
        no_ticket_reason: "",
        remark: "",
      },

      drawerVisible: false,
      formRules: {
        no_ticket_reason: [
          { required: true, message: "请输入关单原因", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["user"]),
    hasPermission() {
      return this.user.d_level && this.user.d_level === "top";
    },
  },
  created() {
    // 页面渲染前执行
    // console.log(this.orderRow);
    // console.log(this.user);
    this.getDetail();
  },
  mounted() {
    // 页面渲染后执行
  },

  methods: {
    getDetail() {
      this.loading = true;
      this.$myApi.flightOrder
        .getDetail({ order_no: this.currRow.order_no })
        .then((res) => {
          this.orderRow = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    cancelTicket() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$myApi.flightOrder
            .manualTicketCancel({
              order_no: this.currRow.order_no,
              error_message: this.form.no_ticket_reason,
              remark: this.form.remark,
            })
            .then((res) => {
              this.$message.success("操作成功");
              this.handleClose();
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    handleOpen() {
      this.drawerVisible = true;
    },
    handleClose() {
      this.drawerVisible = false;
      this.$emit("refresh");
    },
  },
};
</script>

<style lang="scss" scoped>
// ::v-deep .el-form-item {
//   margin-right: 0 !important;
// }
// ::v-deep .el-form-item__label {
//   position: absolute;
// }
::v-deep .auto-width .el-form-item__content {
  width: 100%;
  //   padding-left: 80px;
}
// ::v-deep .el-select,
// .el-input_inner {
//   width: 100%;
// }
</style>
