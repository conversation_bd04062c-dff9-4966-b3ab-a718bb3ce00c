from datetime import datetime, timed<PERSON>ta
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.config import settings
from app.consts.status import OrderStatus
from app.models.flight_order import FlightOrder
from app.services import fare_service, order_services, task_services
from app.services.operate_log_services import OrderOpLogServices
from app.views.schemas import public_schemas, task_schemas
from commons.consts.api_codes import ApiCodes
from commons.fastapi.schemas import common_schemas
from commons.sdks import flight_order


routers = APIRouter(prefix=f'{settings.API_PERFIX}/public', tags=['对外开放的API接口'])


# 普通接口
@routers.post("/order/create", summary="订单创建接口", response_model=common_schemas.BaseApiOut)
async def create_order(item: flight_order.public.FlightOrderPublicCreateOrderRequest):
    params = item.model_dump(exclude_none=True, exclude_unset=True)
    flight_order_row = await order_services.create_order(params)

    await order_services.get_real_pnr(flight_order_row=flight_order_row)
    return ApiCodes.SUCCESS.generate_api_result(data=True)


@routers.post("/order/finish", summary="订单完成接口", response_model=common_schemas.BaseApiOut)
async def finish_order(item: flight_order.public.FlightOrderPublicFinishOrderRequest):
    order_oplog = OrderOpLogServices(admin_id=0, username='system', username_desc='接口')
    await order_services.finish_order(item.order_no, order_oplog)
    return ApiCodes.SUCCESS.generate_api_result(data=True)


@routers.post("/order/cancel", summary="订单取消接口", response_model=common_schemas.BaseApiOut)
async def cancel_order(item: flight_order.public.FlightOrderPublicCancelOrderRequest):
    order_oplog = OrderOpLogServices(admin_id=0, username='system', username_desc='接口')
    order_row = await FlightOrder.get_by_async(FlightOrder.order_no == item.order_no)
    # 不取消，仅记录日志
    if order_row:
        await order_oplog.cancel_order(id=order_row['id'], op_data={'order_info': order_row, 'message': '接收成功'})
        # 通知运价取消临时订单
        await fare_service.finish_tmp_order(mock_pnr=order_row['mock_pnr'])
    return ApiCodes.SUCCESS.generate_api_result(data=True)


@routers.post("/order/task/start", summary="订单任务开始接口", response_model=common_schemas.BaseApiOut)
async def start_order_task(item: flight_order.public.FlightOrderPublicStartTaskRequest):
    affected = await task_services.start_check(item.order_no, item.task_type)
    return ApiCodes.SUCCESS.generate_api_result(data=affected)
