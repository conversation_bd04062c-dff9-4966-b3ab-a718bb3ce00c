import os
import sys
from typing import Any, Dict, List, Optional
from loguru import logger
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field

from commons.cores.config_manager import ConfigManager
from commons.extensions.logger_extras import DEFAULT_LOG_FORMAT, es_api_sink, es_sink, log_uid, log_server_name


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8')

    ROOT_PATH: str = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))

    DEBUG: bool = Field(False, description="是否开启debug模式")
    #
    LOG_PATH: str = Field(os.path.join(ROOT_PATH, 'logs'), description="日志路径")
    LOG_LEVEL: str = Field('INFO', description="日志等级")
    LOG_FORMAT: str = Field(DEFAULT_LOG_FORMAT, description="日志格式")
    LOG_BACKTRACE: bool = Field(False, description="是否开启日志回溯")
    LOG_DIAGNOSE: bool = Field(True, description="是否开启日志诊断")
    LOG_ENQUEUE: bool = Field(True, description="保证进程、线程安全")
    ES_LOG_TAG: str = Field('elasticsearch', description="ES日志标签")
    ES_LOG_LEVEL: str = Field('INFO', description="ES日志等级")
    ES_LOG_ROTATION: str = Field('500 MB', description="ES日志文件大小")
    # ========= REDIS ==========
    REDIS_URL: str = Field(..., description="redis连接url")
    REDIS_MAX_CONNECTIONS: int = Field(50, description="redis最大连接数")
    REDIS_DECODE_RESPONSES: bool = Field(True, description="redis是否解码响应")
    REDIS_SOCKET_TIMEOUT: int = Field(10, description="redis连接超时时间，单位秒，仅同步方式生效")
    REDIS_HEALTH_CHECK_INTERVAL: int = Field(60, description="redis健康检查间隔，单位秒，仅异步方式生效")

    # ========= FastAPI ==========
    FASTAPI_INIT_OPTIONS: Optional[Dict] = Field(
        {'docs_url': '', 'redoc_url': '', 'openapi_url': '', 'swagger_ui_oauth2_redirect_url': ''},
        description="fastapi配置",
    )

    FASTAPI_CORS_ORIGINS: Optional[List[str]] = Field(
        ['http://localhost:9528', 'http://127.0.0.1:9528'], description="跨域白名单"
    )

    API_PERFIX: str = Field('/api/v1/crawler/vj', description="api前缀")
    # ========= 业务配置 ==========
    BOOK_QUEUE: str = Field('tr_book_queue', description="预订任务队列")
    PAY_QUEUE: str = Field('tr_pay_queue', description="支付任务队列")
    ORDER_CHECK_QUEUE: str = Field('tr_check_queue', description="检查任务队列")
    FLIGHT_FARE_URL: str = Field('http://127.0.0.1:8080', description="运价模块API地址")
    FLIGHT_ORDER_URL: str = Field('http://127.0.0.1:8081', description="订单模块API地址")
    FLIGHT_PRE_ORDER_URL: str = Field('http://*************:8082', description="压位模块API地址")
    LOGIN_URL: str = Field(f'/api/v1/flight_fare/admin/login/token', description="登录url,注意前缀配置成运价系统地址")
    PAY_CENTER_URL: str = Field('http://*************:9529', description="支付中心API地址")

    CELERY_BROKER_URL: str = Field(..., description="celery任务池地址")
    CELERY_RESULT_BACKEND: str = Field(..., description="celery结果后端地址")
    CELERY_TASK_SERIALIZER: str = Field('json', description="celery任务序列化类型")
    CELERY_RESULT_SERIALIZER: str = Field('json', description="celery结果序列化类型")
    CELERY_ACCEPT_CONTENT: List[str] = Field(['json'], description="celery任务接受内容")
    CELERY_BROKER_CONNECTION_RETRY: bool = Field(True, description="celery对链接失败进行重试")
    CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP: bool = Field(True, description="celery启动时进行链接重试")

    # 任务状态相关配置
    CELERY_TASK_ACKS_LATE: bool = Field(True, description="任务执行完成后再确认，避免任务丢失")
    CELERY_TASK_REJECT_ON_WORKER_LOST: bool = Field(True, description="worker异常退出时拒绝任务")
    CELERY_TASK_TRACK_STARTED: bool = Field(True, description="追踪任务开始状态")
    CELERY_RESULT_EXPIRES: int = Field(3600, description="结果过期时间，1小时")
    CELERY_TASK_SOFT_TIME_LIMIT: int = Field(3600, description="任务软超时时间，1小时")
    CELERY_TASK_TIME_LIMIT: int = Field(7200, description="任务硬超时时间，2小时")
    CELERY_BROKER_TRANSPORT_OPTIONS: Dict[str, Any] = Field(
        {
            'max_retries': 30,  # 最大重试次数
            'interval_start': 0.5,  # 初始重试间隔时间（秒）
            'interval_step': 0.5,  # 重试间隔递增步长
            'interval_max': 5,  # 最大重试间隔时间
            'visibility_timeout': 3600,  # 消息可见性超时，1小时
        },
        description="broker传输选项",
    )

    CELERY_TASK_ROUTES: Dict[str, str] = Field(
        {
            # vjs
            'vj_search_task': 'vj_search_queue',
            'vj_verify_task': 'vj_verify_queue',
            'vj_verify_book_task': 'vj_verify_book_queue',
            'vj_scan_book_task': 'vj_scan_book_queue',
            'vj_book_task': 'vj_book_queue',
            'vj_confirm_pay_task': 'vj_confirm_pay_queue',
            # vz
            'vz_search_task': 'vz_search_queue',
            'vz_verify_task': 'vz_verify_queue',
            'vz_verify_book_task': 'vz_verify_book_queue',
            'vz_scan_book_task': 'vz_scan_book_queue',
            'vz_book_task': 'vz_book_queue',
            'vz_confirm_pay_task': 'vz_confirm_pay_queue',
        },
        description="任务路由",
    )
    CELERY_WORKER_HIJACK_ROOT_LOGGER: bool = Field(False, description="是否启用 Celery 接管日志")
    THIRD_PROXY_MOD_NAME: str = Field('', description="第三方代理模块名称")
    PROXIES_BY_GROUPED: Dict[str, List[str]] = Field(
        {'default': [], 'hood': [], 'book': []}, description="按用途分组的代理列表"
    )
    PROXY_POOL_KEY: str = Field(None, description="代理池地址")
    WAIT_PROXY_INTERVAL: float = Field(0.0, description="等待代理间隔,0.0表示可以走本机")
    # 延迟释放系数
    PROXY_UNLOCK_DELAY_RANGE: List[int] = Field([1, 2], description="代理锁延迟系数")
    FORBIDDEN_DELAY: int = Field(600, description="触发封禁的代理延迟时间")
    SCAN_TIMES: int = Field(10, description="扫描次数")
    AES_KEY: str = Field(..., description="AES加密密钥")
    # 任务结束延迟系数
    INNER_IP_MAP: Dict[str, str] = Field(default_factory=dict, description="内网IP映射")

    OPEN_DIRECT_PAY: bool = Field(False, description="是否开启直接支付")
    ENABLE_VZ_SEARCH: bool = Field(False, description="是否开启VZ搜索,用来校正余票")
    CRAWLER_START_CHECK_URL: str = Field(
        f'http://************:9000/api/v1/flight_order/public/order/task/start', description="启动检查任务url"
    )

    DING_TOKEN: Optional[str] = Field(
        '601f25173ba1db9c4cd3f93bd694b153263a0a06b2da34dfd4d894b1f82b762a', description="钉钉token"
    )
    DING_TOKENS: Optional[Dict[str, str]] = Field(
        default_factory=dict, description="钉钉token，格式：{'环境': 'token'}"
    )
    DING_ENV: str = Field('dev', description="钉钉环境")

    # ========== 邮件配置 ==========
    EMAIL_ENABLED: bool = Field(False, description="是否启用邮件发送功能")
    EMAIL_SMTP_HOST: Optional[str] = Field(None, description="SMTP服务器地址")
    EMAIL_SMTP_PORT: int = Field(587, description="SMTP服务器端口")
    EMAIL_SMTP_USER: Optional[str] = Field(None, description="SMTP用户名")
    EMAIL_SMTP_PASSWORD: Optional[str] = Field(None, description="SMTP密码")
    EMAIL_FROM_ADDRESS: Optional[str] = Field(None, description="发件人邮箱地址")
    EMAIL_TO_ADDRESSES: Optional[List[str]] = Field(None, description="收件人邮箱地址列表")


settings = Settings()

new_settings = ConfigManager(
    settings_files=[
        os.path.join(settings.ROOT_PATH, 'configs', 'agent_account_rules.toml'),
        os.path.join(settings.ROOT_PATH, 'configs', 'vz_domestic_thb_rules.toml'),
    ]
)
# ========== 日志配置 ==========
log_server_name.set('vz_crawler')
logger.remove()
logger.add(
    sys.stderr,
    level=settings.LOG_LEVEL,
    format=settings.LOG_FORMAT,
    backtrace=settings.LOG_BACKTRACE,
    diagnose=settings.LOG_DIAGNOSE,
    filter=lambda record: record["extra"].get("write_tag") is None,
)


logger.add(
    es_sink, level="INFO", filter=lambda record: record["extra"].get("write_tag") == 'elasticsearch', serialize=True
)

logger.add(
    es_api_sink, level="INFO", filter=lambda record: record["extra"].get("write_tag") == 'api_es_log', serialize=True
)
logger.opt(exception=True)
logger.configure(extra={"unique_id": log_uid})

logger.debug('config loaded')

SYNC_REDIS_CFG = {
    "redis_url": settings.REDIS_URL.split('?')[0],
    "decode_responses": settings.REDIS_DECODE_RESPONSES,
    "max_connections": settings.REDIS_MAX_CONNECTIONS,
    "socket_timeout": settings.REDIS_SOCKET_TIMEOUT,
}

ASYNC_REDIS_CFG = {
    "redis_url": settings.REDIS_URL.split('?')[0],
    "decode_responses": settings.REDIS_DECODE_RESPONSES,
    "max_connections": settings.REDIS_MAX_CONNECTIONS,
    "health_check_interval": settings.REDIS_HEALTH_CHECK_INTERVAL,
}
