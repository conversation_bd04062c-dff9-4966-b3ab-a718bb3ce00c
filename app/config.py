import contextvars
import os
import sys
from typing import Any, Dict, List, Optional, Union
from loguru import logger
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field

from commons.cores.config_manager import ConfigManager
from commons.extensions.logger_extras import DEFAULT_LOG_FORMAT, es_api_sink, es_sink, log_server_name, log_uid

_global_alert_ext_msg = contextvars.ContextVar("_global_alert_ext_msg", default=None)


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8')

    ROOT_PATH: str = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
    STATIC_PATH: str = os.path.join(ROOT_PATH, 'statics')
    DEBUG: bool = Field(False, description="是否开启debug模式")
    #
    LOG_PATH: str = Field(os.path.join(ROOT_PATH, 'logs'), description="日志路径")
    LOG_LEVEL: str = Field('INFO', description="日志等级")
    LOG_FORMAT: str = Field(DEFAULT_LOG_FORMAT, description="日志格式")
    LOG_BACKTRACE: bool = Field(False, description="是否开启日志回溯")
    LOG_DIAGNOSE: bool = Field(False, description="是否开启日志诊断")
    ES_LOG_TAG: str = Field('elasticsearch', description="ES日志标签")
    ES_LOG_LEVEL: str = Field('INFO', description="ES日志等级")
    ES_LOG_ROTATION: str = Field('500 MB', description="ES日志文件大小")
    # ========= MYSQL ==========
    # 异步操作数据库
    SQLALCHEMY_DATABASE_URI: str = Field(
        'mysql+aiomysql://root:password@localhost/basename', description="异步数据库连接url"
    )
    SQLALCHEMY_ECHO: bool = Field(False, description="是否开启sqlalchemy echo")
    # 每n秒检查一次连接池（重要，可避免链接超时断开）
    SQLALCHEMY_POOL_RECYCLE: int = Field(7200, description="每n秒检查一次连接池（重要，可避免链接超时断开）")
    # 连接池最大连接数
    SQLALCHEMY_POOL_SIZE: int = Field(50, description="连接池最大连接数")
    # 连接池最大等待时间
    SQLALCHEMY_POOL_TIMEOUT: int = Field(30, description="连接池最大等待时间")
    # 连接池超出最大连接数时，最大超出上限
    SQLALCHEMY_MAX_OVERFLOW: int = Field(10, description="连接池超出最大连接数时，最大超出上限")
    # ========= 业务配置 ==========
    SALE_PLATFORM: str = Field(..., description="销售平台")
    # ========= REDIS ==========
    REDIS_URL: str = Field(
        'redis://localhost:6379/1?health_check_interval=60&decode_responses=True', description="redis连接url"
    )
    # ========= FastAPI ==========
    FASTAPI_INIT_OPTIONS: Optional[Dict] = Field(
        {'docs_url': '', 'redoc_url': '', 'openapi_url': '', 'swagger_ui_oauth2_redirect_url': ''},
        description="fastapi配置",
    )

    FASTAPI_CORS_ORIGINS: Optional[List[str]] = Field(
        ['http://localhost:9528', 'http://127.0.0.1:9528'], description="跨域白名单"
    )

    API_PERFIX: str = Field('/api/v1', description="api前缀")
    # openssl rand -hex 32
    SECRET_KEY: str = Field(..., description="密钥")  # = env.str('SECRET_KEY')
    ALGORITHM: str = Field('HS256', description="加密算法")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(60 * 24 * 7, description="token过期时间")  # 7 天

    # ========= celery配置 ==========
    CELERY_BROKER_URL: str = Field('redis://localhost:6379/1', description="celery任务池地址")
    CELERY_TASK_SERIALIZER: str = Field('json', description="celery任务序列化类型")
    CELERY_RESULT_SERIALIZER: str = Field('json', description="celery结果序列化类型")
    CELERY_ACCEPT_CONTENT: List[str] = Field(['json'], description="celery任务接受内容")
    CELERY_BROKER_CONNECTION_RETRY: bool = Field(
        True, description="celery对链接失败进行重试"
    )  # celery_broker_connection_retry
    CELERY_BROKER_CONNECTION_RETRY_ON_STARTUP: bool = Field(True, description="celery启动时进行链接重试")
    CELERY_TASK_ROUTES: Dict[str, str] = Field(
        {'tb_shopping_push_task': 'tb_shopping_push_queue'}, description="celery task routes"
    )
    CELERY_WORKER_HIJACK_ROOT_LOGGER: bool = Field(False, description="是否启用 Celery 接管日志")
    # ========= 业务配置 ==========

    FLIGHT_FARE_URL: str = Field(..., description="运价模块API地址")
    # 将同步报价的api与平台调用时的api分开
    FLIGHT_FARE_FOR_SYNC_URL: str = Field('http://*************:9528', description="运价模块API地址")
    FLIGHT_ORDER_URL: str = Field('http://127.0.0.1:8081', description="订单模块API地址")
    FLIGHT_PRE_ORDER_URL: str = Field('http://*************:8082', description="压位模块API地址")
    CRAWLER_URL: str = Field('http://*************:9529', description="压位模块API地址")

    TB_HOST: str = Field('http://gw.api.taobao.com/router/rest', description="tb接口地址")
    TB_CID: str = Field(description="客户ID")
    TB_CHANNEL_MAP: Dict[str, int] = Field(
        {'tb_norm': 3601, 'tb_gold': 3614, 'tb_baggage': 3616}, description="渠道映射"
    )
    TB_CHANNEL_ID: int = Field(description="渠道ID")
    TB_AES_KEY: str = Field(description="客户端AES密钥，用于加密请求体")
    TB_APP_KEY: str = Field(description="客户端APP_KEY")
    TB_APP_SECRET: str = Field(description="客户端APP_SECRET")
    TB_SESSION_KEY: str = Field(description="客户端SESSION_KEY")
    TB_SESSION_REFRESH_TOKEN: str = Field(description="客户端SESSION_REFRESH_TOKEN")
    TB_MOCK_PNR: bool = Field(False, description="是否模拟PNR")
    TB_DEFAULT_VALID_TIME: int = Field(30, description="默认有效时间")
    TB_MIN_TICKET_QUANTITY: int = Field(0, description="tb最小可售票数")
    TB_TEMP_ORDER_LOCK_TIME: int = Field(5, description="临时订单锁定时间,单位分钟")

    DING_TOKEN: str = Field('10fbf11e9796b78aeb0807b8158bce1195ca2e78a8b4c0f4c49eb47cd518c10a', description="钉钉token")
    DING_ENV: str = Field('dev', description="钉钉环境")
    TB_VERIFY_IGNORE_DING_MSG: List[str] = Field([], description="tb验证忽略钉钉消息")
    TB_ALLOW_CREATE_ORDER_AIRLINES: List[str] = Field(['VZ'], description="允许创建订单的航司")
    # 平台4个接口超时都是25秒
    TB_VERIFY_LOOP_TIMES: int = Field(20, description="验价1，实时校验最大循环次数")
    TB_CREATE_LOOP_TIMES: int = Field(20, description="生单，创建订单最大循环次数")
    TB_PAY_VERIFY_LOOP_TIMES: int = Field(20, description="支付前校验最大循环次数")

    # 接口超时时间配置（秒）
    TB_VERIFY_TIMEOUT: int = Field(18, description="验价接口超时时间，单位秒")
    TB_ORDER_CREATE_TIMEOUT: int = Field(18, description="生单接口超时时间，单位秒")

    TB_WAIT_TMP_ORDER: int = Field(30, description="等待临时订单时间,单位秒,默认15秒")
    TB_JST_PROXY: str = Field(None, description="聚石塔代理地址")

    # 多人订单余票保护系数
    TB_MULTI_ORDER_TICKET_PROTECT_COEFFICIENT: Union[int, float] = Field(1, description="多人订单余票保护系数")
    TB_BIDDING_RANGE: bool = Field(True, description="tb竞价范围")


settings = Settings()

# 全局默认实例
price_floating_rules = ConfigManager(
    settings_files=[
        os.path.join(settings.ROOT_PATH, 'configs', 'price_floating_rules.toml'),
        # os.path.join(settings.ROOT_PATH, 'configs', f'price_floating_rules.prod.toml'),
    ]
)


# ========== 日志配置 ==========
log_server_name.set('platform')

logger.remove()
logger.add(
    sys.stderr,
    level=settings.LOG_LEVEL,
    format=settings.LOG_FORMAT,
    backtrace=settings.LOG_BACKTRACE,
    diagnose=settings.LOG_DIAGNOSE,
    filter=lambda record: record["extra"].get("write_tag") is None,
)

logger.add(
    es_sink,
    level=settings.LOG_LEVEL,
    filter=lambda record: record["extra"].get("write_tag") == 'elasticsearch',
    serialize=True,
)

logger.add(
    es_api_sink,
    level=settings.LOG_LEVEL,
    filter=lambda record: record["extra"].get("write_tag") == 'api_es_log',
    serialize=True,
)
logger.opt(exception=True)
logger.configure(extra={"unique_id": log_uid})

logger.debug('config loaded')
