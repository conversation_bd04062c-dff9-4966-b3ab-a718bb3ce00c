import asyncio
from collections import defaultdict
from datetime import datetime
import os
import random
import re
import time
from typing import Union

from loguru import logger
import orjson


from app.consts.status import AirlineOrderStatus, OrderStatus
from app.models.operate_log import OperateLog

from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import SuccessStatus


#


class OperateLogBaseServices:
    """
    压位投放操作日志
    """

    def __init__(self, admin_id: int, username: str, username_desc: str = '') -> None:
        self.admin_id = admin_id
        self.username = username
        self.username_desc = username_desc

    async def save_log(self, op_table: str, row_id: int, op_type: str, desc_tpl: str, op_data: dict):
        op_log_row = None
        try:
            op_time = datetime.now()
            # 通过defaultdict创建一个默认值为空字符串的字典
            op_desc = desc_tpl.format_map(defaultdict(lambda: '未修改', op_data))

            op_log_row = await OperateLog.create_at_async(
                op_table=op_table,
                row_id=row_id,
                admin_id=self.admin_id,
                username=f'{self.username}({self.username_desc})' if self.username_desc else self.username,
                op_type=op_type,
                op_time=op_time,
                op_desc=op_desc,
                op_data=orjson.dumps(op_data).decode('utf-8'),
            )
        except Exception as e:
            logger.exception(f"操作日志写入失败: {e}")

        return op_log_row


class OrderOpLogServices(OperateLogBaseServices):
    TEMPLATES = {''}

    def __init__(self, admin_id: int, username: str, username_desc: str = '') -> None:
        super().__init__(admin_id, username, username_desc)
        self.table = 'flight_orders'

    async def do_create(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='创建订单', desc_tpl="{order_info[ota_order_no]}", op_data=op_data
        )

    async def do_push(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='自动订座推送', desc_tpl="{message}", op_data=op_data
        )

    async def do_verify_book_push(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='预占座推送', desc_tpl="{message}", op_data=op_data)

    async def do_confirm_pay_push(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='确认支付推送', desc_tpl="{message}", op_data=op_data
        )

    async def notice_pre_order(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='压位收单通知', desc_tpl="{message}", op_data=op_data
        )

    async def pre_order_get(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='压位订单占座信息查询', desc_tpl="{message}", op_data=op_data
        )

    async def book_start_check(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='占座前检查', desc_tpl="{message}", op_data=op_data)

    async def book_result(self, id: int, op_data: dict):
        tpl = []
        logger.info(op_data)
        # if 'params' in op_data and op_data['params'].get('data'):
        #     tpl.append(f"异步支付：{op_data['params']['data']['async_pay']}")
        tpl.append("{message}")
        await self.save_log(
            op_table=self.table, row_id=id, op_type='出票回调', desc_tpl='，'.join(tpl), op_data=op_data
        )

    async def verify_book_result(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='预占座回调', desc_tpl="{message}", op_data=op_data)

    async def confirm_pay_result(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='确认支付回调', desc_tpl="{message}", op_data=op_data
        )

    async def finish_order(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='平台验真完成', desc_tpl="{message}", op_data=op_data
        )

    async def do_manual_cancel(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table,
            row_id=id,
            op_type='手工关单',
            desc_tpl='，'.join(["原因：{params[error_message]}", "备注：{params[remark]}", "操作结果：{message}"]),
            op_data=op_data,
        )

    async def cancel_order(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='平台取消订单', desc_tpl="{message}", op_data=op_data
        )

    async def pay_start_check(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='支付前检查', desc_tpl="{message}", op_data=op_data)

    async def pay_push(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='支付推送', desc_tpl="{message}", op_data=op_data)

    async def pay_result(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table,
            row_id=id,
            op_type='支付结果回传',
            desc_tpl="，".join(
                [
                    "支付账号：{params[data][pay_account]}",
                    "支付金额：{params[data][pay_amount]}",
                    "币种：{params[data][currency_code]}",
                    "交易流水：{params[data][trade_no]}",
                    "{message}",
                ]
            ),
            op_data=op_data,
        )

    async def check_push(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='订单检查推送', desc_tpl="{message}", op_data=op_data
        )

    async def check_result(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table,
            row_id=id,
            op_type='订单检查结果回传',
            desc_tpl="，".join(
                [f"回传状态：{AirlineOrderStatus(op_data['params']['data']['order_status']).label}", "{message}"]
            ),
            op_data=op_data,
        )

    async def do_lock(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='锁定', desc_tpl="{message}", op_data=op_data)

    async def do_unlock(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='解锁', desc_tpl="{message}", op_data=op_data)

    async def do_confirm_ticket(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table,
            row_id=id,
            op_type='手工出票',
            desc_tpl="，".join(
                [
                    "PNR：{params[pnr]}",
                    "支付金额：{params[pay_amount]}",
                    "币种：{params[currency_code]}",
                    "支付账号：{params[pay_account]}",
                    "交易流水：{params[trade_no]}",
                    "是否自动检查：{params[book_check]}",
                    "备注：{params[remark]}",
                    "操作结果：{message}",
                ]
            ),
            op_data=op_data,
        )

    async def do_no_ticket(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table,
            row_id=id,
            op_type='确认无票',
            desc_tpl='，'.join(
                [
                    "原因：{params[error_message]}",
                    "备注：{params[remark]}",
                    "异常信息：{order_info[error_message]}",
                    "{message}",
                ]
            ),
            op_data=op_data,
        )

    async def do_to_except(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table,
            row_id=id,
            op_type='订单转异常',
            desc_tpl=''.join(
                [
                    f"从 {OrderStatus(op_data['params']['src_status']).label}({op_data['params']['src_status']}))",
                    f" 转为 {OrderStatus(op_data['params']['dst_status']).label}({op_data['params']['dst_status']})",
                    "，{message}",
                ]
            ),
            op_data=op_data,
        )

    async def do_confirm_ticket_callback(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table,
            row_id=id,
            op_type='出票成功通知平台',
            desc_tpl="PNR：{order_info[pnr]}，{message}",
            op_data=op_data,
        )

    async def do_issue_ticket(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table,
            row_id=id,
            op_type='出票回调',
            desc_tpl="PNR：{order_info[pnr]}，{message}",
            op_data=op_data,
        )

    async def do_no_ticket_callback(self, id: int, op_data: dict):
        await self.save_log(
            op_table=self.table, row_id=id, op_type='出票失败通知平台', desc_tpl="{message}", op_data=op_data
        )

    async def do_re_book(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='重新下单', desc_tpl="{message}", op_data=op_data)

    async def do_re_pay(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='重新支付', desc_tpl="{message}", op_data=op_data)

    async def do_re_check(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='重新检查', desc_tpl="{message}", op_data=op_data)

    async def do_re_callback(self, id: int, op_data: dict):
        await self.save_log(op_table=self.table, row_id=id, op_type='重新回调', desc_tpl="{message}", op_data=op_data)

    async def get_list(self, id: int, limit: int = 20):
        log_rows = await OperateLog.get_all_async(
            OperateLog.op_table == self.table,
            OperateLog.row_id == id,
            limit=limit,
            order_by=[OperateLog.op_time.desc(), OperateLog.id.desc()],
        )

        return log_rows

    async def do_create_pay_card(self, id: int, op_data: dict):
        tpl = []
        if 'card_id' in op_data:
            tpl.append(f'卡ID：{op_data["card_id"]}')
        if 'card_no_suffix' in op_data:
            tpl.append(f'尾号：{op_data["card_no_suffix"]}')
        tpl.append("{message}")
        await self.save_log(
            op_table=self.table, row_id=id, op_type='创建虚拟卡', desc_tpl="，".join(tpl), op_data=op_data
        )

    async def do_get_pay_card_secret(self, id: int, op_data: dict):
        tpl = []
        if 'card_id' in op_data:
            tpl.append(f'卡ID：{op_data["card_id"]}')
        if 'card_no_suffix' in op_data:
            tpl.append(f'尾号：{op_data["card_no_suffix"]}')
        tpl.append("{message}")
        await self.save_log(
            op_table=self.table, row_id=id, op_type='获取虚拟卡密', desc_tpl="，".join(tpl), op_data=op_data
        )
