import copy
from datetime import datetime
from math import ceil
import math
import os
import random
import time

from fastapi import HTTPException
import or<PERSON><PERSON>
from sqlalchemy import and_, or_, update
import sqlalchemy
from app.consts.status import OrderStatus
from app.consts.types import FareType
from app.extensions import redis_locker
from app.extensions.db_extras import get_db_session_async
from app.models.flight_order import FlightOrder
from app.models.order_addon import OrderAddOn
from app.models.order_contact import OrderContact
from app.models.order_passenger import OrderPassenger
from app.models.order_segment import OrderSegment
from app.models.baggage_order import BaggageOrder
from app.models.segment_stops import SegmentStops
from app.models.pay_record import PayRecord
from app.sdks.flight_pre_order_sdk import FlightPreOrderSdk

from app.services import fare_service
from app.services.operate_log_services import OrderOpLogServices
from app.views.schemas import order_schemas, public_schemas
from loguru import logger
from app.config import settings, new_settings
from commons.consts.api_codes import ApiCodes
from commons.consts.common_status import EnableStatus
from commons.consts.common_types import YesOrNo
from sqlalchemy import select

from commons.sdks.base import SdkClient
from commons.sdks.core import get_sub_sdk
from commons.sdks.pay_center.exchange_rate import GetExchangeRateRequest


# def parse_segments(order_no, trips):
#     segments_result = []
#     stops_result = []
#     includes_result = []
#     for trip in trips:
#         trip_index = trip['trip_index']
#         for segment in trip['segments']:
#             segment_index = segment['segment_index']
#             # 行程信息
#             tmp = {
#                 # 唯一键
#                 'order_no': order_no,
#                 'trip_index': trip_index,
#                 'segment_index': segment_index,
#                 'dep_city_code': segment.get('dep_city_code', ''),
#                 # 'dep_city_name': segment.get('dep_city_name', ''),
#                 'arr_city_code': segment.get('arr_city_code', ''),
#                 # 'arr_city_name': segment.get('arr_city_name', ''),
#                 'dep_airport_code': segment['dep_airport_code'],
#                 # 'dep_airport_name': segment.get('dep_airport_name', ''),
#                 'arr_airport_code': segment['arr_airport_code'],
#                 # 'arr_airport_name': segment.get('arr_airport_name', ''),
#                 'dep_time': f'{segment["dep_date"]} {segment["dep_time"]}',
#                 'arr_time': f'{segment["arr_date"]} {segment["arr_time"]}',
#                 'flight_no': segment['flight_no'],
#                 'cabin_code': segment['cabin']['code'],
#                 'cabin_class': segment['cabin']['cabin_class'],
#                 'cabin_name': segment['cabin']['name'],
#                 'cabin_desc': segment['cabin']['desc'],
#             }
#             segments_result.append(tmp)

#             # 经停信息
#             for stop in segment['stops']:
#                 tmp = {
#                     'order_no': order_no,
#                     'trip_index': trip_index,
#                     'segment_index': segment_index,
#                     'stop_airport_code': stop['stop_airport_code'],
#                     'stop_airport_name': stop['stop_airport_name'],
#                     'stop_arr_time': stop['stop_arr_time'],
#                     'stop_dep_time': stop['stop_dep_time'],
#                 }
#                 stops_result.append(tmp)

#             # 包含信息
#             for include in segment['includes']:
#                 tmp = {
#                     'order_no': order_no,
#                     'trip_index': trip_index,
#                     'segment_index': segment_index,
#                     'code': include['code'],
#                     'name': include['name'],
#                     'desc': include['desc'],
#                     'price': include['price'],
#                 }
#                 includes_result.append(tmp)

#     return segments_result, stops_result, includes_result


# def parse_contact(order_no, contact):
#     result = {
#         'order_no': order_no,
#         'contact_name': contact['contact_name'],
#         'mobile': contact['mobile'],
#         'email': contact['email'],
#         'country_code': contact['country_code'],
#     }

#     return result


# def parse_passengers(order_no, passengers):
#     passenger_list = []
#     addon_list = []

#     for passenger in passengers:
#         card_no = passenger['card_no']
#         p = {
#             'order_no': order_no,
#             'passenger_type': passenger['passenger_type'],
#             'passenger_name': passenger['passenger_name'],
#             'sex': passenger['sex'],
#             'country_code': passenger['country_code'],
#             'mobile': passenger['mobile'],
#             'card_type': passenger['card_type'],
#             'card_no': card_no,
#             'card_valid_date': passenger['card_valid_date'],
#             'card_country_code': passenger['card_country_code'],
#             'last_name': passenger['last_name'],
#             'first_name': passenger['first_name'],
#             'birthday': passenger['birthday'],
#             'ticket_no': passenger['ticket_no'],
#             'base_price': passenger['base_price'],
#             'tax': passenger['tax'],
#             'total_price': passenger['total_price'],
#         }
#         passenger_list.append(p)

#         # 附加服务
#         for item in passenger['add_ons']:
#             trip_index = item['trip_index']
#             segment_index = item['segment_index']
#             add_on_items = item['add_on_items']

#             for addon in add_on_items:
#                 a = {
#                     'order_no': order_no,
#                     'trip_index': trip_index,
#                     'segment_index': segment_index,
#                     'card_no': card_no,
#                     'code': addon['code'],
#                     'name': addon['name'],
#                     'desc': addon['desc'],
#                     'price': addon['price'],
#                     'num': addon.get('num', 1),
#                     'total_price': addon['price'] * addon.get('num', 1),
#                 }
#                 addon_list.append(a)
#     return passenger_list, addon_list


async def create_order(params: dict):
    order_info = copy.deepcopy(params['order_info'])
    order_no = order_info['ota_order_no']  # 使用ota_order_no作为系统订单号
    created_ids = {'flight_order': None, 'segments': [], 'passengers': [], 'baggage': [], 'contact': None}
    flight_order_row = None
    try:
        async with get_db_session_async() as session:
            # 检查订单是否已存在
            existing_order = await FlightOrder.get_by_async(FlightOrder.order_no == order_no)
            if existing_order:
                logger.info(f"订单 {order_no} 已存在，跳过创建")
                return existing_order

            total_price = order_info['total_price']
            if order_info['ota_code'] == 'taobao':
                total_price = total_price

            try:
                # 1. 创建订单主表记录
                flight_order_row = await FlightOrder.create_at_async(
                    ota_code=order_info['ota_code'],
                    order_no=order_no,
                    airline_code=order_info['airline_code'],
                    dep_city_code=order_info['dep_city_code'],
                    arr_city_code=order_info['arr_city_code'],
                    dep_airport_code=order_info['dep_airport_code'],
                    arr_airport_code=order_info['arr_airport_code'],
                    trip_type=order_info['trip_type'],
                    dep_date=datetime.strptime(order_info['dep_date'], '%Y-%m-%d').date(),
                    return_date=(
                        datetime.strptime(order_info['return_date'], '%Y-%m-%d').date()
                        if order_info['return_date']
                        else None
                    ),
                    adult_num=order_info['adult_num'],
                    child_num=order_info['child_num'],
                    infant_num=order_info['infant_num'],
                    fare_key=order_info['fare_key'],
                    fare_type=order_info['fare_type'],
                    fare_snapshot=order_info['fare_snapshot'],
                    mock_pnr=order_info['mock_pnr'],
                    pnr=order_info.get('pnr', ''),
                    order_currency='CNY',
                    flight_nos=order_info['flight_nos'],
                    total_price=total_price,
                    expire_time=datetime.strptime(order_info['expire_time'], '%Y-%m-%d %H:%M:%S'),
                    src_currency=order_info['src_currency'],
                    src_adult_base=order_info['src_adult_base'],
                    src_adult_tax=order_info['src_adult_tax'],
                    status=order_info.get('status', OrderStatus.INIT_ORDER.value),
                    auto_book=(
                        EnableStatus.ENABLED.value
                        if order_info['airline_code'] in settings.AUTO_BOOK_AIRLINES
                        else EnableStatus.DISABLED.value
                    ),
                    order_snapshot=orjson.dumps(params).decode('utf-8'),
                )
                # session.add(flight_order)
                # await session.flush()
                created_ids['flight_order'] = flight_order_row['id']

                # 2. 创建航段信息
                for trip in params['trips']:
                    for segment in trip['segments']:
                        order_segment = await OrderSegment.create_at_async(
                            order_no=order_no,
                            trip_index=trip['trip_index'],
                            segment_index=segment['segment_index'],
                            dep_city_code=segment['dep_airport_code'],
                            arr_city_code=segment['arr_airport_code'],
                            dep_airport_code=segment['dep_airport_code'],
                            arr_airport_code=segment['arr_airport_code'],
                            dep_time=datetime.strptime(
                                f"{segment['dep_date']} {segment['dep_time']}", '%Y-%m-%d %H:%M'
                            ),
                            arr_time=datetime.strptime(
                                f"{segment['arr_date']} {segment['arr_time']}", '%Y-%m-%d %H:%M'
                            ),
                            flight_no=segment['flight_no'],
                            cabin_code=segment['cabin']['code'],
                            cabin_class=segment['cabin']['cabin_class'],
                            cabin_name=segment['cabin']['name'],
                            cabin_desc=segment['cabin']['desc'],
                        )
                        # session.add(order_segment)
                        # await session.flush()
                        created_ids['segments'].append(order_segment['id'])

                # 3. 创建乘客信息
                for passenger in params['passengers']:
                    base_price = passenger['base_price']
                    tax = passenger['tax']
                    sub_total_price = passenger['total_price']
                    # if order_info['ota_code'] == 'taobao':
                    #     base_price = base_price / 100
                    #     tax = tax / 100
                    #     total_price = total_price / 100

                    order_passenger = await OrderPassenger.create_at_async(
                        order_no=order_no,
                        segment_id=created_ids['segments'][0] if created_ids['segments'] else None,
                        passenger_type=passenger['passenger_type'],
                        passenger_name=passenger['passenger_name'],
                        last_name=passenger['last_name'],
                        first_name=passenger['first_name'],
                        sex=passenger['sex'],
                        country_code=passenger['country_code'],
                        mobile=passenger['mobile'],
                        card_type=passenger['card_type'],
                        card_no=passenger['card_no'],
                        card_valid_date=passenger['card_valid_date'],
                        card_country_code=passenger['card_country_code'],
                        birthday=passenger['birthday'],
                        ticket_no=passenger['ticket_no'],
                        base_price=base_price,
                        tax=tax,
                        total_price=sub_total_price,
                        sub_pnr=passenger.get('sub_pnr', ''),
                    )
                    # session.add(order_passenger)
                    # await session.flush()
                    created_ids['passengers'].append(order_passenger['id'])

                    # 4. 创建行李信息
                    for baggage in passenger['baggage_orders']:
                        baggage_order = await BaggageOrder.create_at_async(
                            order_no=order_no,
                            outer_order_no=baggage.get('outer_id', ''),
                            passenger_id=order_passenger['id'],
                            baggage_type=baggage.get('baggage_type', ''),
                            baggage_weight=baggage.get('baggage_weight', 0),
                            baggage_size=baggage.get('baggage_size', 0),
                            baggage_count=baggage.get('baggage_count', 0),
                            baggage_price=baggage.get('baggage_price', 0),
                        )
                        # session.add(baggage_order)
                        # await session.flush()
                        created_ids['baggage'].append(baggage_order['id'])

                # 5. 创建联系人信息
                contact = params['contact']
                order_contact = await OrderContact.create_at_async(
                    order_no=order_no,
                    contact_name=contact['contact_name'],
                    mobile=contact['mobile'],
                    email=contact['email'],
                    country_code=contact['country_code'],
                )
                # session.add(order_contact)
                # await session.flush()
                created_ids['contact'] = order_contact['id']

                # 提交所有更改
                await session.commit()

                # 记录操作日志
                order_oplog = OrderOpLogServices(admin_id=0, username='system', username_desc='接口')
                await order_oplog.do_create(id=flight_order_row['id'], op_data=params)
                return flight_order_row

            except Exception as e:
                # 发生错误时回滚并清理已创建的数据
                await session.rollback()
                logger.exception(f"创建订单失败: {str(e)}")

                # 清理已创建的数据
                if created_ids['flight_order']:
                    await FlightOrder.delete_all_async(FlightOrder.id == created_ids['flight_order'])
                if created_ids['segments']:
                    await OrderSegment.delete_all_async(OrderSegment.id.in_(created_ids['segments']))
                if created_ids['passengers']:
                    await OrderPassenger.delete_all_async(OrderPassenger.id.in_(created_ids['passengers']))
                if created_ids['baggage']:
                    await BaggageOrder.delete_all_async(BaggageOrder.id.in_(created_ids['baggage']))
                if created_ids['contact']:
                    await OrderContact.delete_all_async(OrderContact.id == created_ids['contact'])

                raise HTTPException(500, f"创建订单失败: {str(e)}")

    except Exception as e:
        logger.exception(f"创建订单失败: {str(e)}")
        raise HTTPException(500, f"创建订单失败: {str(e)}")


async def get_real_pnr(flight_order_row: dict):
    try:
        fare_sdk = get_sub_sdk(module_name='flight_fare', host=settings.FLIGHT_FARE_URL)
        mock_pnr = flight_order_row['mock_pnr']
        resp = await fare_sdk.fare_public_get_verify_order_async(mock_pnr=mock_pnr)
        real_pnr = resp.get('data', {}).get('real_pnr')
        logger.debug(f'订单 {flight_order_row["order_no"]} 的临时PNR {mock_pnr} 预占座结果: {resp}')
        if real_pnr:
            # 预占座成功
            if flight_order_row['pnr']:
                real_pnr = flight_order_row['pnr']
            real_pnr_expire_time = resp.get('data', {}).get('expire_time')
            account_name = resp.get('data', {}).get('book_result', {}).get('account_name', '')
            channel_price = (
                resp.get('data', {}).get('book_result', {}).get('book', {}).get('fare', {}).get('total_amount', 0)
            )
            if not channel_price:
                channel_price = (
                    resp.get('data', {}).get('book_result', {}).get('book', {}).get('fare', {}).get('total_price', 0)
                )
            currency_code = (
                resp.get('data', {}).get('book_result', {}).get('book', {}).get('fare', {}).get('currency', '')
            )
            if not currency_code:
                currency_code = resp.get('data', {}).get('src_currency', '')
            fare_snapshot = orjson.loads(flight_order_row['fare_snapshot'])
            channel_cny_price = 0
            if currency_code == fare_snapshot['products'][0]['adult']['src_currency']:
                rate = (
                    fare_snapshot['products'][0]['adult']['src_cny_total']
                    / fare_snapshot['products'][0]['adult']['src_total']
                )
                channel_cny_price = math.ceil(rate * channel_price)
            else:
                logger.warning(
                    f'订单 {flight_order_row["order_no"]} 的货币代码不一致，{currency_code} != {fare_snapshot["products"][0]["adult"]["src_currency"]}'
                )
            logger.debug(f'订单 {flight_order_row["order_no"]} 的预占座结果: {resp}')
            await FlightOrder.update_by_async(
                FlightOrder.id == flight_order_row['id'],
                FlightOrder.status.in_([OrderStatus.INIT_ORDER.value, OrderStatus.NEW_ORDER.value]),
                status=OrderStatus.NEW_ORDER.value,
                pnr=real_pnr,
                pnr_expire_time=real_pnr_expire_time,
                channel_account=account_name,
                channel_price=channel_price,
                channel_currency_code=currency_code,
                channel_cny_price=channel_cny_price,
            )
        elif resp.get('code') == ApiCodes.FARE_VERIFY_STOP_BOOK.value:
            # 无法预占座的订单，直接出票
            await FlightOrder.update_by_async(
                FlightOrder.id == flight_order_row['id'],
                FlightOrder.status == OrderStatus.INIT_ORDER.value,
                status=OrderStatus.NEW_ORDER.value,
            )
        else:
            # 其他错误，直接失败
            await FlightOrder.update_by_async(
                FlightOrder.id == flight_order_row['id'],
                FlightOrder.status.in_([OrderStatus.INIT_ORDER.value, OrderStatus.NEW_ORDER.value]),
                status=OrderStatus.INIT_ORDER_FAIL.value,
                error_code=ApiCodes.ORDER_INIT_FAIL.value,
                error_message=f'订单 {flight_order_row["order_no"]} 未找到预占座结果，请人工核实',
            )
            logger.warning(f'订单 {flight_order_row} 的临时PNR {mock_pnr} 不存在')
    except Exception as e:
        logger.exception(f'获取订单 {flight_order_row} 的临时PNR {mock_pnr} 失败: {str(e)}')


async def manual_ticket_lock(order_no: str, current_admin: dict):
    order_row = await FlightOrder.get_by_async(FlightOrder.order_no == order_no)
    # todo 应判断订单状态
    # todo 应进行平台锁定操作
    logger.debug(current_admin)
    locker_id = order_row['locker_id']
    if locker_id:
        if locker_id != current_admin['id']:
            raise HTTPException(500, f'订单已被 {order_row["locker_name"]} 锁定')

    else:
        key = f'manual_ticket_lock_{order_row["order_no"]}'
        if await redis_locker.lock(key=key, expire=30):
            await FlightOrder.update_by_async(
                FlightOrder.id == order_row['id'],
                FlightOrder.order_no == order_no,
                FlightOrder.locker_id == 0,
                FlightOrder.locker_name == '',
                locker_id=current_admin['id'],
                locker_name=current_admin['username_desc'],
            )
            order_row['locker_id'] = current_admin['id']
            order_row['locker_name'] = current_admin['username_desc']
    return order_row


def is_self_or_super(current_admin: dict, locker_id: int):
    can_op = current_admin['id'] == locker_id or current_admin['is_super'] == YesOrNo.YES.value
    return can_op


async def manual_ticket_unlock(order_no: str, current_admin: dict):
    # 所有需要判断super的地方 current_admin 应使用 get_current_admin_full 获取
    logger.debug(current_admin)
    order_row = await FlightOrder.get_by_async(FlightOrder.order_no == order_no)

    # todo 应判断订单状态
    if is_self_or_super(current_admin, order_row['locker_id']):
        await FlightOrder.update_by_async(
            FlightOrder.id == order_row['id'], FlightOrder.order_no == order_no, locker_id=0, locker_name=''
        )
        key = f'manual_ticket_lock_{order_row["order_no"]}'
        await redis_locker.unlock(key=key)
        order_row['locker_id'] = 0
        order_row['locker_name'] = ''
    else:
        raise HTTPException(500, f'订单已被 {order_row["locker_name"]} 锁定')
    return order_row


# async def manual_ticket_confirm(params: dict, current_admin: dict):
#     order_status = OrderStatus.PAY_SUCCESS.value
#     if not params['book_check']:
#         order_status = OrderStatus.CHECK_SUCCESS.value
#     order_row = await FlightOrder.get_by_async(FlightOrder.order_no == params['order_no'])
#     if order_row['locker_id'] != current_admin['id']:
#         if not order_row['locker_id']:
#             raise HTTPException(500, f'请先执行订单锁定')
#         raise HTTPException(500, f'订单已被 {order_row["locker_name"]} 锁定')
#     if order_row['status'] >= order_status and order_row['pnr'] == params['pnr']:
#         return order_row

#     currency_code = params['currency_code']
#     pay_center_sdk = SdkClient(host=settings.PAY_CENTER_URL)
#     resp = await pay_center_sdk.send_async(
#         request=GetExchangeRateRequest(from_currency=currency_code, to_currency="CNY")
#     )
#     rate = resp['data']['rate']
#     cny_amount = math.ceil(rate * params['pay_amount'])

#     async with get_db_session_async() as session:
#         async with session.begin():
#             try:
#                 session.add(
#                     PayRecord(
#                         order_no=params['order_no'],
#                         pay_account=params['pay_account'],
#                         pay_type=params['pay_type'],
#                         pay_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
#                         status=OrderStatus.PAY_SUCCESS.value,
#                         pay_amount=params['pay_amount'],
#                         cny_amount=cny_amount,
#                         pay_message=params.get('remark', ''),
#                         currency_code=params['currency_code'],
#                         trade_no=params['trade_no'],
#                     )
#                 )

#                 query = (
#                     update(FlightOrder)
#                     .where(
#                         FlightOrder.order_no == params['order_no'],
#                         FlightOrder.status >= OrderStatus.PAY_FAIL.value,
#                         FlightOrder.status < OrderStatus.PAY_SUCCESS.value,
#                         # or_(
#                         #     # 压位订单不判断锁定
#                         #     FlightOrder.status == OrderStatus.PRE_ORDER_BOOKING.value,
#                         #     # 正常订单需要判断锁定
#                         #     and_(
#                         FlightOrder.locker_id == current_admin['id'],
#                         FlightOrder.locker_name == current_admin['username_desc'],
#                         #     ),
#                         # ),
#                     )
#                     .values(
#                         status=order_status,
#                         channel_code=params['channel_code'],
#                         channel_name=params['channel_name'],
#                         channel_price=params['pay_amount'],
#                         channel_currency_code=params['currency_code'],
#                         channel_account=params.get('channel_account', ''),
#                         pnr=params['pnr'],
#                         remark=params.get('remark', ''),
#                         locker_id=0,
#                         locker_name='',
#                         ticket_issuer_id=current_admin['id'],
#                         ticket_issuer_name=current_admin['username_desc'],
#                     )
#                 )
#                 await session.execute(query)

#                 # raise Exception('test')
#                 # await session.commit()
#             except Exception as e:
#                 # await session.rollback()
#                 raise e

#     order_row = await FlightOrder.get_by_async(FlightOrder.order_no == params['order_no'])
#     return order_row


# async def no_ticket(params: dict, current_admin: dict):
#     order_row = await FlightOrder.get_by_async(FlightOrder.order_no == params['order_no'])
#     if order_row['status'] >= OrderStatus.PAY_SUCCESS.value:
#         raise HTTPException(500, f'已支付的订单无法无票')

#     if order_row['locker_id'] != current_admin['id']:
#         if not order_row['locker_id']:
#             raise HTTPException(500, f'请先执行订单锁定')
#         raise HTTPException(500, f'订单已被 {order_row["locker_name"]} 锁定')

#     await FlightOrder.update_by_async(
#         FlightOrder.order_no == params['order_no'],
#         FlightOrder.status >= OrderStatus.PAY_FAIL.value,
#         FlightOrder.status < OrderStatus.PAY_SUCCESS.value,
#         FlightOrder.locker_id == current_admin['id'],
#         FlightOrder.locker_name == current_admin['username_desc'],
#         status=OrderStatus.NO_TICKET.value,
#         # error_message=params['error_message'],
#         remark=params['error_message'],
#         locker_id=0,
#         locker_name='',
#         ticket_issuer_id=current_admin['id'],
#         ticket_issuer_name=current_admin['username_desc'],
#     )
#     new_row = await FlightOrder.get_by_async(FlightOrder.order_no == params['order_no'])
#     return new_row


async def is_expire(order_row: dict):
    order_expire = datetime.strptime(order_row['expire_time'], '%Y-%m-%d %H:%M:%S')
    if order_expire < datetime.now():
        await FlightOrder.update_by_async(
            FlightOrder.id == order_row['id'],
            FlightOrder.status == order_row['status'],
            status=OrderStatus.BOOK_FAIL.value,
            error_code=ApiCodes.ORDER_EXPIRE.value,
            error_message=ApiCodes.ORDER_EXPIRE.label,
        )
        logger.info(f'订单 {order_row["order_no"]} 已超时，跳过')
        return True

    return False


async def is_auto_book(order_row: dict):
    if order_row['airline_code'] not in new_settings.get('auto_book.airlines', []):
        await FlightOrder.update_by_async(
            FlightOrder.id == order_row['id'],
            FlightOrder.status == order_row['status'],
            status=OrderStatus.BOOK_FAIL.value,
            error_code=ApiCodes.ORDER_AUTO_BOOK_ERROR.value,
            error_message=ApiCodes.ORDER_AUTO_BOOK_ERROR.label,
        )
        return False
    return True


async def compare_order(order_row: dict, book_result: dict, update_info: dict):

    if order_row['flight_nos'] != book_result['flight_no']:
        return (
            ApiCodes.ORDER_COMPARE_ERROR.value,
            f'{ApiCodes.ORDER_COMPARE_ERROR.label}: 航班号不一致，{order_row["flight_nos"]} != {book_result["flight_no"]}',
        )
    elif order_row['dep_airport_code'] != book_result['dep_airport_code']:

        return (
            ApiCodes.ORDER_COMPARE_ERROR.value,
            f'{ApiCodes.ORDER_COMPARE_ERROR.label}: 出发机场不一致，{order_row["dep_airport_code"]} != {book_result["dep_airport_code"]}',
        )
    elif order_row['arr_airport_code'] != book_result['arr_airport_code']:
        return (
            ApiCodes.ORDER_COMPARE_ERROR.value,
            f'{ApiCodes.ORDER_COMPARE_ERROR.label}: 到达机场不一致，{order_row["arr_airport_code"]} != {book_result["arr_airport_code"]}',
        )
    elif order_row['dep_date'] != book_result['dep_date']:
        return (
            ApiCodes.ORDER_COMPARE_ERROR.value,
            f'{ApiCodes.ORDER_COMPARE_ERROR.label}: 出发时间不一致，{order_row["dep_date"]} != {book_result["dep_date"]}',
        )

    passenger_row = await OrderPassenger.get_all_async(OrderPassenger.order_no == order_row['order_no'])
    row_keys = [f'{p["last_name"]}{p["first_name"]}_{p["card_no"]}' for p in passenger_row]
    p_keys = [f'{p["last_name"]}{p["first_name"]}_{p["card_no"]}' for p in book_result['passengers']]
    # 求列表差异
    diff = list(set(row_keys) - set(p_keys))
    if diff:
        return ApiCodes.ORDER_COMPARE_ERROR.value, f'{ApiCodes.ORDER_COMPARE_ERROR.label}: 乘客信息不一致，{diff}'

    # if order_row['total_price'] < update_info['channel_cny_price']:
    #     return (
    #         ApiCodes.ORDER_COMPARE_ERROR.value,
    #         f'{ApiCodes.ORDER_COMPARE_ERROR.label}: 订单总价小于预订总价，{order_row["total_price"]} < {update_info["channel_cny_price"]}',
    #     )

    if book_result['total_price'] <= 0:
        return (
            ApiCodes.ORDER_COMPARE_ERROR.value,
            f'{ApiCodes.ORDER_COMPARE_ERROR.label}: 订单总价小于等于0，{order_row["total_price"]}',
        )

    # if not update_info['pnr'] and not book_result['trade_no']:
    #     return (ApiCodes.ORDER_COMPARE_ERROR.value, f'{ApiCodes.ORDER_COMPARE_ERROR.label}: 订单PNR为空')

    return ApiCodes.SUCCESS.value, ApiCodes.SUCCESS.label


async def get_update_info(book_result: dict):

    currency_code = book_result['currency_code']
    pay_center_sdk = SdkClient(host=settings.PAY_CENTER_URL)
    resp = await pay_center_sdk.send_async(
        request=GetExchangeRateRequest(from_currency=currency_code, to_currency="CNY")
    )
    rate = resp['data']['rate']

    update_info = {
        'channel_code': book_result['flight_no'][:2],
        'channel_price': book_result['total_price'],
        'channel_cny_price': math.ceil(rate * book_result['total_price']),
        'channel_currency_code': currency_code,
        'pnr': book_result.get('pnr', ''),
        'pay_url': book_result.get('pay_url', ''),
        'pay_limit_time': book_result.get('pay_limit_time'),
        'last_book_snapshot': orjson.dumps(book_result).decode('utf-8'),
        'book_end_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'channel_account': book_result.get('extra', {}).get('account', {}).get('email', ''),
    }
    return update_info


async def to_except(item: order_schemas.ToExceptIn, current_admin: dict, order_oplog: OrderOpLogServices):
    order_row = await FlightOrder.get_by_async(FlightOrder.id == item.id)
    if order_row['locker_id'] > 0 and order_row['locker_id'] != current_admin['id']:
        raise HTTPException(
            status_code=400,
            detail=f"订单已被{order_row['locker_name']}锁定，无法转为 {OrderStatus(item.dst_status).label}({item.dst_status})",
        )
    if order_row['status'] != item.src_status:
        raise HTTPException(
            status_code=400, detail=f"状态已改变为 {OrderStatus(order_row['status']).label}({order_row['status']})"
        )

    if item.src_status not in settings.TO_EXCEPT_MAP.get(item.dst_status, []):
        allow_status = ','.join(
            [f'{OrderStatus(s).label}({s})' for s in settings.TO_EXCEPT_MAP.get(item.dst_status, [])]
        )
        raise HTTPException(
            status_code=400,
            detail=f"只有 {allow_status} 可以转为 {OrderStatus(item.dst_status).label}({item.dst_status})",
        )

    affected = await FlightOrder.update_by_async(
        FlightOrder.id == item.id,
        FlightOrder.order_no == item.order_no,
        FlightOrder.status == item.src_status,
        status=item.dst_status,
    )
    if not affected:
        raise HTTPException(status_code=400, detail=f"订单状态已改变，请刷新页面后重试")

    await order_oplog.do_to_except(
        id=item.id,
        op_data={
            'order_info': order_row,
            'params': item.model_dump(exclude_none=True, exclude_unset=True),
            'message': '成功',
        },
    )
    return affected


async def re_book(item: order_schemas.ReBookIn, current_admin: dict, order_oplog: OrderOpLogServices):
    order_row = await FlightOrder.get_by_async(FlightOrder.id == item.id)
    if order_row['locker_id'] > 0 and order_row['locker_id'] != current_admin['id']:
        raise HTTPException(status_code=400, detail=f"订单已被{order_row['locker_name']}锁定，无法重新下单")

    if order_row['status'] != item.src_status:
        raise HTTPException(
            status_code=400, detail=f"状态已改变为 {OrderStatus(order_row['status']).label}({order_row['status']})"
        )

    if order_row['pnr']:
        raise HTTPException(status_code=400, detail=f"该订单已有PNR，无法重新下单")

    if order_row['status'] <= OrderStatus.PAY_FAIL.value or order_row['status'] >= OrderStatus.PAY_SUCCESS.value:
        raise HTTPException(
            status_code=400,
            detail=f"该订单状态为 {OrderStatus(order_row['status']).label}({order_row['status']})，无法重新下单",
        )

    affected = await FlightOrder.update_by_async(
        FlightOrder.id == item.id,
        FlightOrder.order_no == item.order_no,
        FlightOrder.status == item.src_status,
        status=OrderStatus.NEW_ORDER.value,
        book_start_time=None,
        book_retry_times=FlightOrder.book_retry_times + 1,
    )
    if not affected:
        raise HTTPException(status_code=400, detail=f"订单状态已改变，请刷新页面后重试")

    await order_oplog.do_re_book(
        id=item.id,
        op_data={
            'order_info': order_row,
            'params': item.model_dump(exclude_none=True, exclude_unset=True),
            'message': '成功',
        },
    )


async def re_pay(item: order_schemas.RePayIn, current_admin: dict, order_oplog: OrderOpLogServices):
    order_row = await FlightOrder.get_by_async(FlightOrder.id == item.id)
    if order_row['locker_id'] > 0 and order_row['locker_id'] != current_admin['id']:
        raise HTTPException(status_code=400, detail=f"订单已被{order_row['locker_name']}锁定，无法重新支付")

    if order_row['status'] != item.src_status:
        raise HTTPException(
            status_code=400, detail=f"状态已改变为 {OrderStatus(order_row['status']).label}({order_row['status']})"
        )

    if order_row['status'] <= OrderStatus.CHECK_FAIL.value or order_row['status'] >= OrderStatus.CHECK_SUCCESS.value:
        raise HTTPException(
            status_code=400,
            detail=f"该订单状态为 {OrderStatus(order_row['status']).label}({order_row['status']})，无法重新支付",
        )

    affected = await FlightOrder.update_by_async(
        FlightOrder.id == item.id,
        FlightOrder.order_no == item.order_no,
        FlightOrder.status == item.src_status,
        status=OrderStatus.BOOK_SUCCESS.value,
        pay_start_time=None,
        pay_retry_times=FlightOrder.pay_retry_times + 1,
    )
    if not affected:
        raise HTTPException(status_code=400, detail=f"订单状态已改变，请刷新页面后重试")

    await order_oplog.do_re_pay(
        id=item.id,
        op_data={
            'order_info': order_row,
            'params': item.model_dump(exclude_none=True, exclude_unset=True),
            'message': '成功',
        },
    )


# async def re_check(item: order_schemas.ReCheckIn, current_admin: dict, order_oplog: OrderOpLogServices):
#     order_row = await FlightOrder.get_by_async(FlightOrder.id == item.id)
#     if order_row['locker_id'] > 0 and order_row['locker_id'] != current_admin['id']:
#         raise HTTPException(status_code=400, detail=f"订单已被{order_row['locker_name']}锁定，无法重新检查")

#     if order_row['status'] != item.src_status:
#         raise HTTPException(
#             status_code=400, detail=f"状态已改变为 {OrderStatus(order_row['status']).label}({order_row['status']})"
#         )

#     if order_row['status'] <= OrderStatus.CALLBACK_FAIL.value or order_row['status'] >= OrderStatus.CHECK_SUCCESS.value:
#         raise HTTPException(
#             status_code=400,
#             detail=f"该订单状态为 {OrderStatus(order_row['status']).label}({order_row['status']})，无法重新检查",
#         )

#     affected = await FlightOrder.update_by_async(
#         FlightOrder.id == item.id,
#         FlightOrder.order_no == item.order_no,
#         FlightOrder.status == item.src_status,
#         status=OrderStatus.PAY_SUCCESS.value,
#         check_start_time=None,
#         check_retry_times=FlightOrder.check_retry_times + 1,
#     )
#     if not affected:
#         raise HTTPException(status_code=400, detail=f"订单状态已改变，请刷新页面后重试")

#     await order_oplog.do_re_check(
#         id=item.id,
#         op_data={
#             'order_info': order_row,
#             'params': item.model_dump(exclude_none=True, exclude_unset=True),
#             'message': '成功',
#         },
#     )


async def get_issue_passengers(order_row: dict):
    passenger_rows = await OrderPassenger.get_all_async(OrderPassenger.order_no == order_row['order_no'])
    issue_passengers = [
        {
            'name': p['passenger_name'],
            'pnr': p['sub_pnr'] if p['sub_pnr'] else order_row['pnr'],
            'ticket_nos': [p['sub_pnr'] if p['sub_pnr'] else order_row['pnr']],
            'segments': [{'dep_city_code': order_row['dep_city_code'], 'arr_city_code': order_row['arr_city_code']}],
        }
        for p in passenger_rows
    ]
    return issue_passengers


async def issue_ticket(order_no: str, current_admin: dict, order_oplog: OrderOpLogServices):
    order_row = await FlightOrder.get_by_async(FlightOrder.order_no == order_no)
    if order_row['locker_id'] > 0 and order_row['locker_id'] != current_admin['id']:
        raise HTTPException(status_code=400, detail=f"订单已被{order_row['locker_name']}锁定，无法出票")

    if order_row['status'] not in [OrderStatus.BOOK_SUCCESS.value, OrderStatus.CALLBACK_FAIL.value]:
        raise HTTPException(
            status_code=400,
            detail=f"只有状态为 {OrderStatus.BOOK_SUCCESS.label}({OrderStatus.BOOK_SUCCESS.value}) 和 {OrderStatus.CALLBACK_FAIL.label}({OrderStatus.CALLBACK_FAIL.value}) 的订单才能执行出票回调",
        )
    platform_host = new_settings.get('api_rules.hosts.flight_platform')
    if not platform_host:
        platform_host = new_settings.get('api_rules.hosts.default')

    issue_passengers = await get_issue_passengers(order_row)
    platform_sdk = get_sub_sdk('platform', platform_host)
    resp = await platform_sdk.platform_callback_ticket_result_async(
        order_no=order_no, ota_code=order_row['ota_code'], passengers=issue_passengers
    )
    if resp.get('code', -1) == ApiCodes.SUCCESS.value:
        await FlightOrder.update_by_async(
            FlightOrder.order_no == order_no,
            FlightOrder.status.in_([OrderStatus.BOOK_SUCCESS.value, OrderStatus.CALLBACK_FAIL.value]),
            # FlightOrder.status == OrderStatus.BOOK_SUCCESS.value,
            status=OrderStatus.VERIFYING.value,
        )
        await order_oplog.do_issue_ticket(id=order_row['id'], op_data={'order_info': order_row, 'message': '成功'})
    else:
        await FlightOrder.update_by_async(
            FlightOrder.order_no == order_no,
            FlightOrder.status == OrderStatus.BOOK_SUCCESS.value,
            status=OrderStatus.CALLBACK_FAIL.value,
            error_code=resp['code'],
            error_message=resp.get('message', ''),
        )
        await order_oplog.do_issue_ticket(
            id=order_row['id'], op_data={'order_info': order_row, 'message': resp.get('message', '')}
        )

        ApiCodes.UNKNOWN.raise_error(f"出票回调失败: {resp.get('message', '')}")


async def re_callback(item: order_schemas.ReCallbackIn, current_admin: dict, order_oplog: OrderOpLogServices):
    order_row = await FlightOrder.get_by_async(FlightOrder.id == item.id)
    if order_row['locker_id'] > 0 and order_row['locker_id'] != current_admin['id']:
        raise HTTPException(status_code=400, detail=f"订单已被{order_row['locker_name']}锁定，无法重新回调")

    if order_row['status'] != item.src_status:
        raise HTTPException(
            status_code=400, detail=f"状态已改变为 {OrderStatus(order_row['status']).label}({order_row['status']})"
        )

    affected = 0
    if order_row['status'] == OrderStatus.CALLBACK_FAIL.value:
        affected = await FlightOrder.update_by_async(
            FlightOrder.id == item.id,
            FlightOrder.order_no == item.order_no,
            FlightOrder.status == OrderStatus.CALLBACK_FAIL.value,
            status=OrderStatus.CHECK_SUCCESS.value,
        )
    elif order_row['status'] == OrderStatus.CANCEL_BOOK_FAIL.value:
        affected = await FlightOrder.update_by_async(
            FlightOrder.id == item.id,
            FlightOrder.order_no == item.order_no,
            FlightOrder.status == OrderStatus.CANCEL_BOOK_FAIL.value,
            status=OrderStatus.NO_TICKET.value,
        )
    else:
        raise HTTPException(
            status_code=400,
            detail=f"只有状态为 {OrderStatus.CALLBACK_FAIL.label}({OrderStatus.CALLBACK_FAIL.value}) 和 {OrderStatus.CANCEL_BOOK_FAIL.label}({OrderStatus.CANCEL_BOOK_FAIL.value}) 的订单才能重新回调",
        )
    if not affected:
        raise HTTPException(status_code=400, detail=f"订单状态已改变，请刷新页面后重试")

    await order_oplog.do_re_callback(
        id=item.id,
        op_data={
            'order_info': order_row,
            'params': item.model_dump(exclude_none=True, exclude_unset=True),
            'message': '成功',
        },
    )


async def finish_order(order_no: str, order_oplog: OrderOpLogServices):
    order_row = await FlightOrder.get_by_async(FlightOrder.order_no == order_no)
    await FlightOrder.update_by_async(FlightOrder.order_no == order_no, status=OrderStatus.CALLBACK_SUCCESS.value)
    await order_oplog.finish_order(id=order_row['id'], op_data={'order_info': order_row, 'message': '成功'})

    await fare_service.finish_tmp_order(mock_pnr=order_row['mock_pnr'])


async def cancel_order(params: dict, current_admin: dict, order_oplog: OrderOpLogServices):
    order_no = params['order_no']
    order_row = await FlightOrder.get_by_async(FlightOrder.order_no == order_no)
    if order_row['locker_id'] != current_admin['id']:
        if not order_row['locker_id']:
            raise HTTPException(500, f'请先执行订单锁定')
        raise HTTPException(500, f'订单已被 {order_row["locker_name"]} 锁定')
    affected = await FlightOrder.update_by_async(
        FlightOrder.order_no == order_no,
        FlightOrder.status == OrderStatus.BOOK_FAIL.value,
        status=OrderStatus.PLATFORM_CANCEL.value,
    )
    if not affected:
        await order_oplog.do_manual_cancel(
            id=order_row['id'], op_data={'order_info': order_row, 'message': '失败', 'params': params}
        )
        # await fare_service.finish_tmp_order(mock_pnr=order_row['mock_pnr'])
        ApiCodes.ORDER_STATUS_ERROR.raise_error(f'订单 {order_no} 状态错误，无法取消')
    else:
        await order_oplog.do_manual_cancel(
            id=order_row['id'], op_data={'order_info': order_row, 'message': '成功', 'params': params}
        )
        # await fare_service.finish_tmp_order(mock_pnr=order_row['mock_pnr'])
    if order_row:
        await fare_service.finish_tmp_order(mock_pnr=order_row['mock_pnr'])


def book_task_valid(order_row: dict):
    # 只有待出票、推送出票失败、出票失败 可以推送自动出票任务
    if order_row['status'] < OrderStatus.BOOK_FAIL.value or order_row['status'] >= OrderStatus.BOOK_SUCCESS.value:
        ApiCodes.ORDER_STATUS_ERROR.raise_error(
            f'订单 {order_row["order_no"]} 状态为 {OrderStatus(order_row["status"]).label}({order_row["status"]})，无法推送自动出票任务'
        )

    return True


def confirm_pay_task_valid(order_row: dict):
    if order_row['status'] < OrderStatus.PAY_FAIL.value or order_row['status'] >= OrderStatus.BOOK_SUCCESS.value:
        ApiCodes.ORDER_STATUS_ERROR.raise_error(
            f'订单 {order_row["order_no"]} 状态为 {OrderStatus(order_row["status"]).label}({order_row["status"]})，无法推送确认支付任务'
        )
    if order_row['pnr'] is None:
        ApiCodes.ORDER_PNR_ERROR.raise_error(f'订单 {order_row["order_no"]} 没有PNR，无法推送确认支付任务')
    if order_row['pnr_expire_time'] is None:
        ApiCodes.ORDER_PNR_EXPIRE_TIME_ERROR.raise_error(
            f'订单 {order_row["order_no"]} 没有PNR过期时间，无法推送确认支付任务'
        )
    if datetime.strptime(order_row['pnr_expire_time'], '%Y-%m-%d %H:%M:%S') < datetime.now():
        ApiCodes.ORDER_PNR_EXPIRE_TIME_ERROR.raise_error(
            f'订单 {order_row["order_no"]} 的PNR已过期，无法推送确认支付任务'
        )
    return True


def verify_book_task_valid(order_row: dict):
    if order_row['status'] < OrderStatus.PRE_BOOK_FAIL.value or order_row['status'] >= OrderStatus.BOOK_SUCCESS.value:
        ApiCodes.ORDER_STATUS_ERROR.raise_error(
            f'订单 {order_row["order_no"]} 状态为 {OrderStatus(order_row["status"]).label}({order_row["status"]})，无法推送预占座任务'
        )

    return True
