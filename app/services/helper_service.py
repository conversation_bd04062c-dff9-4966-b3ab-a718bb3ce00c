from loguru import logger
from app.consts.currency import CurrencyCodes
from app.services import vj_search_service
from commons import sdks
from commons.sdks.base import SdkClient
from app.config import settings
import pandas as pd
import datetime
import time
from typing import Dict, List, Set, Tuple
import random
from app.clients.vj_client import VJClient
import json
import os
from pathlib import Path


# 默认的示例航线，仅在无法读取 JSON 文件时使用
DEFAULT_ROUTES = ['BKK-HDY', 'HDY-BKK']


def is_thailand_domestic_route(dep_airport: str, arr_airport: str) -> bool:
    """
    判断是否为泰国境内线

    Args:
        dep_airport: 出发机场代码
        arr_airport: 到达机场代码

    Returns:
        bool: 如果出发和到达机场都在泰国境内则返回True
    """
    try:
        from app.config import new_settings

        # 从配置文件获取泰国机场列表
        vz_rules = new_settings.get('vz_domestic_thb_rules')
        if not vz_rules:
            return False

        thailand_airports = set(airport.upper() for airport in vz_rules.get('thailand_airports', []))
        return dep_airport.upper() in thailand_airports and arr_airport.upper() in thailand_airports

    except Exception as e:
        logger.error(f"检查泰国境内线时出错: {e}")
        return False


def should_use_thb_for_vz_domestic(task_data: dict) -> bool:
    """
    检查VZ境内线A舱是否应该使用THB币种

    Args:
        task_data: 查询任务数据

    Returns:
        bool: 如果符合VZ境内线A舱条件则返回True
    """
    try:
        from app.config import new_settings

        # 获取VZ境内线THB规则配置
        vz_rules = new_settings.get('vz_domestic_thb_rules')
        if not vz_rules or not vz_rules.get('enabled', False):
            return False

        # 检查航空公司代码
        airline_code = task_data.get('airline_code', '').upper()
        if airline_code not in [code.upper() for code in vz_rules.get('airline_codes', [])]:
            return False

        # 检查是否为境内线
        dep_airport = task_data.get('dep_airport_code', '').upper()
        arr_airport = task_data.get('arr_airport_code', '').upper()
        if not is_thailand_domestic_route(dep_airport, arr_airport):
            return False

        # 检查舱位等级（必须明确指定A舱）
        cabin_class = task_data.get('cabin_class', '').upper()
        required_cabins = [code.upper() for code in vz_rules.get('cabin_classes', [])]
        if not cabin_class or cabin_class not in required_cabins:
            return False

        logger.info(f"VZ境内线A舱检测: {dep_airport}-{arr_airport} 符合THB规则")
        return True

    except Exception as e:
        logger.error(f"检查VZ境内线THB规则时出错: {e}")
        return False


def apply_vz_domestic_thb_rule(task_data: dict) -> dict:
    """
    应用VZ境内线A舱THB币种规则

    Args:
        task_data: 原始查询任务数据

    Returns:
        dict: 应用规则后的任务数据
    """
    if should_use_thb_for_vz_domestic(task_data):
        try:
            from app.config import new_settings

            vz_rules = new_settings.get('vz_domestic_thb_rules')
            force_currency = vz_rules.get('force_currency', 'THB')

            # 创建修改后的任务数据副本
            modified_task_data = task_data.copy()
            original_currency = modified_task_data.get('currency_code', 'USD')
            modified_task_data['currency_code'] = force_currency

            logger.info(f"VZ境内线A舱规则应用: 币种从 {original_currency} 强制改为 {force_currency}")
            return modified_task_data

        except Exception as e:
            logger.error(f"应用VZ境内线THB规则时出错: {e}")

    return task_data


def load_routes_from_json(json_path: str = None) -> List[str]:
    """从 JSON 文件加载航线数据"""
    if not json_path:
        # 使用默认路径
        json_path = os.path.join(settings.ROOT_PATH, 'statics', 'lines.json')

    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if isinstance(data, list):
                return data
            elif isinstance(data, dict) and 'routes' in data:
                return data['routes']
            else:
                logger.warning(f"JSON 文件格式不正确: {json_path}")
                return DEFAULT_ROUTES
    except Exception as e:
        logger.error(f"读取航线文件失败 {json_path}: {str(e)}")
        return DEFAULT_ROUTES


class FlightDataCollector:
    def __init__(self, query_days: int = 0):
        self.client = VJClient()
        self.error_routes = set()  # 记录错误的航线
        self.no_from_routes = set()  # 记录出发地不存在的航线
        self.no_to_routes = set()  # 记录目的地不存在的航线
        self.flight_frequencies: Dict[str, Set[str]] = {}  # 记录航班在查询天数内出现的日期
        self.collected_flights: Dict[str, dict] = {}  # 记录收集到的航班信息
        # 设置查询天数，0表示查询15天
        self.query_days = 15 if query_days == 0 else query_days

    def _random_delay(self):
        """随机延迟，避免频繁请求"""
        delay = random.uniform(2, 4)
        time.sleep(delay)

    def _parse_flight_info(self, flight_data: dict, route: str, date: str) -> List[dict]:
        """解析航班信息"""
        if not flight_data.get('status') or not flight_data.get('travelOption'):
            return []

        results = []
        travel_options = flight_data["travelOption"].get(route, [])

        for option in travel_options:
            # 跳过中转航班
            if option.get('numberOfChanges', 0) > 0:
                continue

            flights = option.get('flights', [])
            if not flights:
                continue

            flight = flights[0]
            flight_info = self._extract_flight_info(flight)

            # 记录航班出现的日期，用于后续班期计算
            flight_no = flight_info['flight_number']
            if flight_no not in self.flight_frequencies:
                self.flight_frequencies[flight_no] = set()
            self.flight_frequencies[flight_no].add(date)

            # 更新或添加航班信息
            if flight_no not in self.collected_flights:
                self.collected_flights[flight_no] = flight_info

            results.append(flight_info)

        return results

    def _extract_flight_info(self, flight: dict) -> dict:
        """从航班数据中提取所需信息"""
        airline_code = flight['airlineCode']['code']
        flight_number = flight['flightNumber']
        flight_no = airline_code + flight_number

        dep_datetime = datetime.datetime.strptime(flight['departure']['localScheduledTime'], '%Y-%m-%d %H:%M:%S')
        arr_datetime = datetime.datetime.strptime(flight['arrival']['localScheduledTime'], '%Y-%m-%d %H:%M:%S')

        # 计算日期差值来判断是否跨天
        days_diff = (arr_datetime.date() - dep_datetime.date()).days
        day_info = str(days_diff) if days_diff >= 0 else '0'  # 如果计算结果为负，说明数据异常，默认为0

        return {
            'airline_code': airline_code,
            'flight_number': flight_no,
            'departure_code': flight['departure']['airport']['code'],
            'arrival_code': flight['arrival']['airport']['code'],
            'departure_hour': dep_datetime.strftime('%H:%M'),
            'arrival_hour': arr_datetime.strftime('%H:%M'),
            'isShare': 'N',
            'startDate': '2025-01-01',
            'endDate': '2026-01-01',
            'aircraft_model': flight.get('aircraftModel', {}).get('identifier', ''),
            'stopInfo': '',
            'weekInfo': '',  # 将在后续根据频率计算
            'dayInfo': day_info,
        }

    def _calculate_week_info(self, dates: Set[str]) -> str:
        """根据查询天数内航班出现的日期计算班期"""
        if not dates:
            return '1234567'  # 如果没有数据，默认每天都飞

        # 将日期按周几分组
        weekday_flights = {str(i): [] for i in range(1, 8)}  # 1-7 表示周一到周日
        for date in dates:
            dt = datetime.datetime.strptime(date, '%Y-%m-%d')
            weekday = str(dt.isoweekday())
            weekday_flights[weekday].append(date)

        # 如果查询天数大于15天，按周分析
        if self.query_days > 15:
            weeks = self.query_days // 7
            weekdays = set()

            # 对每个工作日进行分析
            for weekday, flight_dates in weekday_flights.items():
                # 计算这个工作日在总周数中出现的频率
                frequency = len(flight_dates) / weeks
                # 如果在超过一半的周中都有航班，认为是固定班期
                if frequency >= 0.5:
                    weekdays.add(weekday)
        else:
            # 15天内的逻辑：统计每个工作日的出现次数和可能的总次数
            weekdays = set()
            start_date = min(dates)
            end_date = max(dates)
            date_range = (
                datetime.datetime.strptime(end_date, '%Y-%m-%d') - datetime.datetime.strptime(start_date, '%Y-%m-%d')
            ).days + 1

            # 计算每个工作日在日期范围内可能出现的次数
            possible_weekdays = {str(i): 0 for i in range(1, 8)}
            current_date = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            for _ in range(date_range):
                weekday = str(current_date.isoweekday())
                possible_weekdays[weekday] += 1
                current_date += datetime.timedelta(days=1)

            # 如果某个工作日至少出现过一次，就认为是固定班期
            for weekday, flight_dates in weekday_flights.items():
                if flight_dates:  # 只要有一次出现就算有效
                    weekdays.add(weekday)

        # 如果没有找到任何固定班期，返回所有出现过航班的日期
        if not weekdays:
            weekdays = {weekday for weekday, dates in weekday_flights.items() if dates}

        return ''.join(sorted(list(weekdays)))

    def collect_route_data(self, dep: str, arr: str, date: str) -> List[dict]:
        """收集单个航线某天的数据"""
        try:
            result = self.client.search(trip_type=1, from_where=dep, to_where=arr, start=date, end=date, currency='usd')

            if isinstance(result, dict):
                if result.get('status') == 0:
                    error_msg = result.get('message', '')
                    if 'to_where' in error_msg:
                        self.no_to_routes.add(f"{dep}-{arr}")
                    if 'from_where' in error_msg:
                        self.no_from_routes.add(f"{dep}-{arr}")
                    return []

                return self._parse_flight_info(result, f"{dep}-{arr}", date)

        except Exception as e:
            logger.error(f"Error collecting data for {dep}-{arr} on {date}: {str(e)}")
            self.error_routes.add(f"{dep}-{arr}")

        return []

    def collect_all_data(self, routes: List[str] = None) -> Tuple[List[dict], List[dict]]:
        """收集所有航线的数据"""
        if routes is None:
            raise ValueError('航线列表不能为空')

        flight_data = []
        airline_data = []

        # 获取指定天数的日期
        dates = [
            (datetime.datetime.now() + datetime.timedelta(days=i)).strftime('%Y-%m-%d')
            for i in range(1, self.query_days + 1)
        ]

        total = len(routes)
        for idx, route in enumerate(routes, 1):
            logger.info(f"正在获取 {route} 的航班信息......{idx}/{total}")

            dep, arr = route.split('-')
            route_flights = []
            route_dates = set()  # 记录该航线在查询天数内有航班的日期

            # 收集指定天数的数据
            for date in dates:
                flights = self.collect_route_data(dep, arr, date)
                if flights:  # 如果当天有航班
                    route_dates.add(date)
                route_flights.extend(flights)
                self._random_delay()  # 添加随机延迟

            if route_flights:
                # 根据收集到的数据计算航班班期
                for flight_no in self.flight_frequencies:
                    week_info = self._calculate_week_info(self.flight_frequencies[flight_no])
                    if flight_no in self.collected_flights:
                        self.collected_flights[flight_no]['weekInfo'] = week_info

                # 计算航线班期（根据查询天数内有航班的日期计算）
                route_week_info = self._calculate_week_info(route_dates)

                # 添加航线信息
                airline_data.append(
                    {
                        'airline': 'VJ',
                        'startDate': '2025-01-01',
                        'endDate': '2026-01-01',
                        'dep': dep,
                        'arr': arr,
                        'weekInfo': route_week_info,  # 使用计算得出的航线班期
                    }
                )

                # 添加当前航线的航班信息
                flight_data.extend(
                    [
                        flight_info
                        for flight_info in self.collected_flights.values()
                        if flight_info['departure_code'] == dep and flight_info['arrival_code'] == arr
                    ]
                )

            # 每处理完一条航线后清空收集的数据，准备处理下一条航线
            self.flight_frequencies.clear()
            self.collected_flights.clear()

        return flight_data, airline_data


def collect_flight_info(routes: List[str] = None, query_days: int = 0, json_path: str = None):
    """收集航班信息的入口函数"""
    # 如果没有提供航线列表，从 JSON 文件加载
    if not routes:
        routes = load_routes_from_json(json_path)

    logger.info(f"查询天数: {query_days if query_days > 0 else 15} 天")
    logger.info(f"航线数量: {len(routes)}")
    logger.info(f"航线文件: {json_path if json_path else '使用默认路径'}")

    # 创建数据收集器实例
    collector = FlightDataCollector(query_days=query_days)

    # 使用指定的航线列表收集数据
    flight_data, airline_data = collector.collect_all_data(routes)

    # 转换为DataFrame并保存
    df_flight = pd.DataFrame(flight_data)
    df_airline = pd.DataFrame(airline_data)

    # 导出到Excel文件
    company = 'VJ'
    flightno_file = f"./data/{company}_flightno.xlsx"
    airline_file = f"./data/{company}_airline.xlsx"

    # 创建输出目录（如果不存在）
    os.makedirs("./data", exist_ok=True)

    df_flight.to_excel(flightno_file, index=False)
    df_airline.to_excel(airline_file, index=False)

    logger.info(f"数据已成功导出到 {flightno_file}, {airline_file}")
    logger.info(f"错误航线列表：{collector.error_routes}")
    logger.info(f"出发地不存在的航线：{collector.no_from_routes}")
    logger.info(f"目的地不存在的航线：{collector.no_to_routes}")


def do_currency_compare(
    dep=None,
    arr=None,
    date=None,
    fnb=None,
    cfg_path=None,
    is_debug=False,
    max_workers=5,
    max_date_offset=7,
    max_retries=20,
):
    """汇率对比

    Args:
        dep: 出发地机场代码
        arr: 到达地机场代码
        date: 航班日期
        fnb: 指定航班号
        cfg_path: 配置文件路径
        is_debug: 是否开启调试模式
        max_workers: 最大并发数量，默认10
        max_date_offset: 最大顺延日期天数，默认7
        max_retries: 最大重试次数，默认20
    """
    from commons.extensions.logger_extras import log_uid
    import re

    # 统一读取汇率文件
    agent_rates = _load_agent_rates()

    # 用于存储选定的航班号，确保相同条件下使用相同航班
    selected_flights = {}

    # 解析配置文件或使用单个查询参数
    queries = []
    if cfg_path:
        if not os.path.exists(cfg_path):
            raise ValueError(f"配置文件不存在: {cfg_path}")
        with open(cfg_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            for item in config:
                query_dep = item['dep']
                query_arr = item['arr']
                query_date = item.get('date')
                query_fnb = item.get('fnb')

                # 如果没有指定日期，默认往后推15天
                if not query_date:
                    query_date = (datetime.datetime.now() + datetime.timedelta(days=15)).strftime('%Y-%m-%d')

                queries.append({'dep': query_dep, 'arr': query_arr, 'date': query_date, 'fnb': query_fnb})
    else:
        if not all([dep, arr, date]):
            raise ValueError("当未指定配置文件时，dep、arr和date为必填参数")
        queries.append({'dep': dep, 'arr': arr, 'date': date, 'fnb': fnb})

    # 创建Excel写入器
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M')
    output_file = f'currency_compare_{timestamp}.xlsx'

    # 优化：一次遍历currency_codes，处理所有查询
    results_by_query, failed_routes = _process_all_queries_optimized(
        queries, selected_flights, is_debug, max_workers, max_date_offset, max_retries
    )

    # 在输出Excel前统一处理汇率转换
    _add_currency_conversion_columns(results_by_query, agent_rates)

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 收集所有线路的数据用于总sheet
        all_results = []

        # 处理每个查询的单独sheet
        for query_key, result in results_by_query.items():
            if result:
                df = pd.DataFrame(result)
                # 处理sheet名称中的非法字符
                sheet_name = query_key
                sheet_name = re.sub(r'[\\/*?:"<>|]', '_', sheet_name)
                df.to_excel(writer, sheet_name=sheet_name, index=False)

                # 格式化：将CNY总价最小的行加粗标红
                _format_min_cny_price_row(writer, sheet_name, df)

                # 收集数据到总sheet
                all_results.extend(result)

                logger.info(f"已完成: {query_key}")
            else:
                logger.warning(f"未找到数据: {query_key}")

        # 创建总sheet
        if all_results:
            all_df = pd.DataFrame(all_results)
            all_df.to_excel(writer, sheet_name='总汇总', index=False)

            # 对总sheet按查询条件分组标红最小CNY总价
            _format_summary_sheet_by_query(writer, '总汇总', all_df)

            logger.info(f"总汇总sheet已创建，包含 {len(all_results)} 条记录")

    logger.info(f"汇率对比结果已导出到: {output_file}")

    # 发送邮件
    try:
        from app.services.email_service import send_currency_compare_result

        # 构建查询信息描述，使用实际查询的日期
        queries_info = []
        for query_key, result in results_by_query.items():
            if result:  # 只显示有结果的查询
                # 从query_key解析出实际的查询信息
                parts = query_key.split('-')
                if len(parts) >= 3:
                    dep = parts[0]
                    arr = parts[1]
                    actual_date = '-'.join(parts[2:])  # 处理日期中可能包含的'-'

                    # 查找对应的原始查询以获取fnb信息
                    fnb = None
                    for query in queries:
                        if query['dep'] == dep and query['arr'] == arr:
                            fnb = query['fnb']
                            break

                    info = f"- {dep} → {arr} ({actual_date})"
                    if fnb:
                        info += f" [指定航班: {fnb}]"
                    queries_info.append(info)

        queries_description = "\n".join(queries_info)

        # 构建失败航线信息
        failed_routes_description = ""
        if failed_routes:
            failed_info_list = []
            for failed_route in failed_routes:
                dep = failed_route['dep']
                arr = failed_route['arr']
                date_range = failed_route['date_range']
                fnb = failed_route['fnb']
                reason = failed_route['reason']

                info = f"- {dep} → {arr} ({date_range})"
                if fnb:
                    info += f" [指定航班: {fnb}]"
                info += f" - {reason}"
                failed_info_list.append(info)

            failed_routes_description = "\n".join(failed_info_list)

        # 发送邮件
        email_sent = send_currency_compare_result(output_file, queries_description, failed_routes_description)
        if email_sent:
            logger.info("汇率对比结果邮件发送成功")
        else:
            logger.warning("汇率对比结果邮件发送失败")

    except Exception as e:
        logger.error(f"发送邮件时出错: {e}")

    return output_file


def compare_prices(
    cfg_path: str = None, currency_codes: str = None, lines: str = None, date: str = None, target_currency: str = "CNY"
):
    """对比不同币种的价格

    Args:
        cfg_path: 配置文件路径
        currency_codes: 币种列表，用逗号分隔
        lines: 航线列表，用逗号分隔
        date: 航班日期
        target_currency: 目标币种，默认为CNY
    """
    from commons.extensions.logger_extras import log_uid
    import json
    import os
    from datetime import datetime

    # 解析配置
    if cfg_path:
        if not os.path.exists(cfg_path):
            raise ValueError(f"配置文件不存在: {cfg_path}")
        with open(cfg_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            currency_codes = config.get('currency_codes', [])
            lines = config.get('lines', [])
            date = config.get('date')
            target_currency = config.get('target_currency', target_currency)
    else:
        if not all([currency_codes, lines, date]):
            raise ValueError("当未指定配置文件时，currency_codes、lines和date为必填参数")
        currency_codes = currency_codes.split(',')
        lines = lines.split(',')

    # 验证日期格式
    try:
        datetime.strptime(date, '%Y-%m-%d')
    except ValueError:
        raise ValueError("日期格式错误，应为YYYY-MM-DD")

    # 初始化结果列表
    results = []
    pay_center_sdk = SdkClient(host=settings.PAY_CENTER_URL)

    # 遍历每个航线
    for line in lines:
        dep, arr = line.split('-')
        log_uid.set(f'{dep}-{arr}-{date}')

        # 获取第一个航班信息
        first_flight = None
        for currency_code in currency_codes:
            vj_search = vj_search_service.VJSearchService()
            # vj_search.set_proxy(proxy_group='default', airline_code='VJ')
            vj_search.vj_client.session.timeout = 10
            time.sleep(random.uniform(1.5, 3))
            search_result = vj_search.run_search(
                {
                    'airline_code': 'VJ',
                    'dep_airport_code': dep,
                    'arr_airport_code': arr,
                    'dep_date': date,
                    'currency_code': currency_code,
                }
            )

            if search_result and search_result.get('data') and search_result['data'].get('results'):
                first_flight = search_result['data']['results'][0]['trips'][0]
                break

        if not first_flight:
            # 如果没有找到任何航班，添加空记录
            for currency_code in currency_codes:
                results.append(
                    {
                        '出发': dep,
                        '到达': arr,
                        '日期': date,
                        '原始币种': 'XXX',
                        '原始金额': 0,
                        '汇率': 0,
                        f'转换为{target_currency}的金额': 0,
                    }
                )
            continue

        # 遍历每个币种获取价格
        for currency_code in currency_codes:
            vj_search = vj_search_service.VJSearchService()
            # vj_search.set_proxy(proxy_group='default', airline_code='VJ')
            vj_search.vj_client.session.timeout = 10
            time.sleep(random.uniform(1.5, 3))
            search_result = vj_search.run_search(
                {
                    'airline_code': 'VJ',
                    'dep_airport_code': dep,
                    'arr_airport_code': arr,
                    'dep_date': date,
                    'currency_code': currency_code,
                }
            )

            if not search_result or not search_result.get('data') or not search_result['data'].get('results'):
                results.append(
                    {
                        '出发': dep,
                        '到达': arr,
                        '日期': date,
                        '原始币种': 'XXX',
                        '原始金额': 0,
                        '汇率': 0,
                        f'转换为{target_currency}的金额': 0,
                    }
                )
                continue

            flight_info = search_result['data']['results'][0]['trips'][0]
            book_currency_code = search_result['data']['exchange']['currency_code']
            base_price = float(flight_info['fares']['adult']['base'])
            tax_price = float(flight_info['fares']['adult']['tax'])
            total_price = base_price + tax_price

            if book_currency_code == target_currency:
                target_price = total_price
                rate = 1
            else:
                resp = pay_center_sdk.send(
                    request=sdks.pay_center.exchange_rate.GetExchangeRateRequest(
                        from_currency=book_currency_code, to_currency=target_currency
                    )
                )
                rate = resp['data']['rate']
                target_price = round(total_price * rate, 2)

            results.append(
                {
                    '出发': dep,
                    '到达': arr,
                    '日期': date,
                    '原始币种': f'{book_currency_code}({CurrencyCodes(book_currency_code).label})',
                    '原始金额': round(total_price, 2),
                    '汇率': round(rate, 6),
                    f'转换为{target_currency}的金额': round(target_price, 2),
                }
            )
            # # TODO break 仅测试用
            # break

    # 导出到Excel
    df = pd.DataFrame(results)
    output_file = f'price_compare_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    df.to_excel(output_file, index=False)
    logger.info(f"价格对比结果已导出到: {output_file}")


def _process_all_queries_optimized(
    queries, selected_flights, is_debug=False, max_workers=10, max_date_offset=7, max_retries=20
):
    """重新设计的并发版本：并发入参{dep, arr, date, currency_list}

    Args:
        queries: 查询列表
        selected_flights: 选定的航班字典
        is_debug: 是否调试模式
        max_workers: 最大并发数量，默认10
        max_date_offset: 最大顺延日期天数，默认7
        max_retries: 最大重试次数，默认20
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed
    import threading

    currency_codes = CurrencyCodes.mappings()
    currency_list = list(currency_codes.keys())
    logger.info(f"支持的币种: {currency_list} (共{len(currency_list)}个)")

    results_by_query = {}
    failed_routes = []  # 收集失败的航线信息

    # 线程锁，只用于保护关键共享资源
    results_lock = threading.Lock()
    flights_lock = threading.Lock()
    failed_routes_lock = threading.Lock()

    def process_single_ftd_with_currencies(dep, arr, date, fnb, currency_list):
        """处理单个ftd条件的所有币种（新逻辑）"""
        query_key = f"{dep}-{arr}-{date}"
        logger.info(f"开始处理航线: {dep}-{arr}-{date}")

        ftd_results = []
        successful_currencies = []
        failed_currencies = []
        actual_date = date  # 记录实际使用的日期

        # 循环处理每个币种
        for i, currency_code in enumerate(currency_list):
            if is_debug and len(successful_currencies) >= 2:  # debug模式只处理2个币种
                break

            is_first_currency = i == 0

            try:
                result = _process_single_currency_new_logic(
                    dep,
                    arr,
                    actual_date,
                    fnb,
                    currency_code,
                    selected_flights,
                    flights_lock,
                    is_first_currency,
                    max_date_offset,
                    max_retries,
                )
                if result:
                    ftd_results.append(result)
                    successful_currencies.append(currency_code)
                    logger.info(f"{dep}-{arr}-{actual_date}-{currency_code} 处理成功")

                    # 如果是第一个币种且发生了日期顺延，更新actual_date
                    if is_first_currency and result.get('航线'):
                        route_parts = result['航线'].split('_')
                        if len(route_parts) >= 3:
                            actual_date = route_parts[-1]
                else:
                    failed_currencies.append(currency_code)
                    logger.warning(f"{dep}-{arr}-{actual_date}-{currency_code} 处理失败")

                    # 如果第一个币种失败，说明该航线在这个时间段没有航班，直接退出
                    if is_first_currency:
                        logger.error(f"{dep}-{arr}-{actual_date} 第一个币种失败，该航线无航班，停止处理后续币种")
                        # 第一个币种失败时不立即记录失败信息，等到最后统一处理
                        break
            except Exception as e:
                failed_currencies.append(currency_code)
                logger.error(f"{dep}-{arr}-{actual_date}-{currency_code} 处理异常: {e}")

                # 如果第一个币种异常，也直接退出
                if is_first_currency:
                    logger.error(f"{dep}-{arr}-{actual_date} 第一个币种异常，停止处理后续币种")
                    # 第一个币种异常时不立即记录失败信息，等到最后统一处理
                    break

        # 检查是否所有币种都成功返回
        total_currencies = len(currency_list) if not is_debug else 2
        success_rate = len(successful_currencies) / total_currencies * 100

        logger.info(
            f"{dep}-{arr}-{actual_date} 完成: 成功 {len(successful_currencies)}/{total_currencies} ({success_rate:.1f}%)"
        )

        if failed_currencies:
            logger.warning(f"{dep}-{arr}-{actual_date} 失败的币种: {failed_currencies}")

        # 保留所有有成功结果的查询，不要求100%成功率
        if len(successful_currencies) > 0:
            with results_lock:
                final_query_key = f"{dep}-{arr}-{actual_date}"
                results_by_query[final_query_key] = ftd_results
                if len(successful_currencies) == total_currencies:
                    logger.info(f"{dep}-{arr}-{actual_date} 所有币种处理完成，共 {len(ftd_results)} 条结果")
                else:
                    logger.warning(f"{dep}-{arr}-{actual_date} 部分币种成功，保留结果，共 {len(ftd_results)} 条结果")
        else:
            logger.error(f"{dep}-{arr}-{actual_date} 所有币种都失败，无结果可保留")

        # 如果有失败的币种，记录失败信息（用于邮件报告）
        if failed_currencies:
            with failed_routes_lock:
                # 计算顺延天数和日期范围
                if actual_date != date:
                    from datetime import datetime

                    original_dt = datetime.strptime(date, '%Y-%m-%d')
                    actual_dt = datetime.strptime(actual_date, '%Y-%m-%d')
                    days_offset = (actual_dt - original_dt).days
                    date_range = f'{date} 至 {actual_date}'
                else:
                    date_range = date

                # 判断失败类型和原因
                if len(successful_currencies) == 0:
                    # 完全失败（第一个币种就失败了）
                    if actual_date != date:
                        reason = f'顺延{days_offset}天后无结果'
                    else:
                        reason = '原始日期无结果'
                else:
                    # 部分失败
                    reason = f'部分币种失败，成功率{success_rate:.1f}% (失败币种: {", ".join(failed_currencies)})'

                failed_info = {
                    'dep': dep,
                    'arr': arr,
                    'original_date': date,
                    'actual_date': actual_date,
                    'date_range': date_range,
                    'fnb': fnb,
                    'reason': reason,
                }
                failed_routes.append(failed_info)

        return query_key, len(successful_currencies), total_currencies

    # 并发处理所有查询
    global_max_workers = min(len(queries), max_workers)
    logger.info(f"全局并发数量: {global_max_workers}, 币种处理: 顺序执行")

    with ThreadPoolExecutor(max_workers=global_max_workers) as executor:
        futures = []
        for query in queries:
            future = executor.submit(
                process_single_ftd_with_currencies,
                query['dep'],
                query['arr'],
                query['date'],
                query['fnb'],
                currency_list,
            )
            futures.append(future)

        # 等待所有任务完成
        for future in as_completed(futures):
            try:
                query_key, success_count, total_count = future.result()
                logger.info(f"查询 {query_key} 完成: {success_count}/{total_count}")
            except Exception as e:
                logger.error(f"查询处理异常: {e}")

    return results_by_query, failed_routes


def _process_single_currency_new_logic(
    dep, arr, date, fnb, currency_code, selected_flights, lock, is_first_currency, max_date_offset=7, max_retries=20
):
    """新逻辑：处理单个币种，包含错误码判断和重试逻辑

    Args:
        max_date_offset: 最大顺延日期天数，默认7
        max_retries: 最大重试次数，默认20（注意：顺延日期不占用重试次数）
    """
    from datetime import datetime, timedelta
    from commons.extensions.logger_extras import log_uid

    log_uid.set(f'{dep}-{arr}-{date}-{currency_code}')

    current_date = date

    # 主要查询逻辑
    for attempt in range(max_retries):
        # 每次run_search前都要调用set_proxy更换代理
        vj_search = vj_search_service.VJSearchService()
        vj_search.vj_client.session.timeout = 10
        vj_search.set_proxy(proxy_group='default', airline_code='VJ')

        search_result = vj_search.run_search(
            {
                'airline_code': 'VJ',
                'dep_airport_code': dep,
                'arr_airport_code': arr,
                'dep_date': current_date,
                'currency_code': currency_code,
            }
        )

        # 判断错误码
        if search_result:
            error_code = search_result.get('error', {}).get('code')

            # 查询成功
            if error_code is None or error_code == 0:
                # 检查是否有航班结果
                if search_result.get('data') and search_result['data'].get('results'):
                    logger.debug(
                        f'{dep}-{arr}-{current_date}-{currency_code} 查询成功: {len(search_result["data"]["results"])} 个选项'
                    )
                    with lock:
                        return _process_search_result(
                            search_result, dep, arr, current_date, fnb, currency_code, selected_flights, True
                        )
                else:
                    # 查询成功，但没有results，判断是否为第一个currency
                    if is_first_currency:
                        logger.info(f'{dep}-{arr}-{current_date}-{currency_code} 查询成功但无航班，开始顺延日期')
                        # 顺延日期，最多顺延max_date_offset天（不占用重试次数）
                        for day_offset in range(1, max_date_offset + 1):
                            new_date = (datetime.strptime(date, '%Y-%m-%d') + timedelta(days=day_offset)).strftime(
                                '%Y-%m-%d'
                            )
                            logger.info(f'{dep}-{arr}-{currency_code} 尝试顺延到 {new_date}')

                            # 顺延查询时也更换代理
                            vj_search_new = vj_search_service.VJSearchService()
                            vj_search_new.vj_client.session.timeout = 10
                            vj_search_new.set_proxy(proxy_group='default', airline_code='VJ')

                            search_result = vj_search_new.run_search(
                                {
                                    'airline_code': 'VJ',
                                    'dep_airport_code': dep,
                                    'arr_airport_code': arr,
                                    'dep_date': new_date,
                                    'currency_code': currency_code,
                                }
                            )

                            if search_result and search_result.get('data') and search_result['data'].get('results'):
                                logger.info(
                                    f'{dep}-{arr}-{new_date}-{currency_code} 顺延成功: {len(search_result["data"]["results"])} 个选项'
                                )
                                with lock:
                                    result = _process_search_result(
                                        search_result, dep, arr, new_date, fnb, currency_code, selected_flights, True
                                    )
                                    # 更新航线信息显示实际查询日期
                                    if result:
                                        result['航线'] = f'{dep}_{arr}_{new_date}'
                                    return result

                        logger.warning(f'{dep}-{arr}-{currency_code} 顺延{max_date_offset}天后仍无结果，直接退出')
                        return None
                    else:
                        # 非第一个币种且无航班结果，可能是临时问题，允许重试几次但不顺延日期
                        logger.warning(
                            f'{dep}-{arr}-{current_date}-{currency_code} 非第一个币种且无航班结果，重试 {attempt + 1}/{max_retries}'
                        )
                        continue
            else:
                # 查询失败，重试，最多重试20次
                logger.warning(
                    f'{dep}-{arr}-{current_date}-{currency_code} 查询失败，错误码: {error_code}，重试 {attempt + 1}/{max_retries}'
                )
                continue
        else:
            logger.warning(
                f'{dep}-{arr}-{current_date}-{currency_code} 查询返回空结果，重试 {attempt + 1}/{max_retries}'
            )
            continue

    logger.error(f'{dep}-{arr}-{current_date}-{currency_code} 重试 {max_retries} 次后仍然失败')
    return None


def _process_search_result(search_result, dep, arr, date, fnb, currency_code, selected_flights, is_first_time_for_ftd):
    """处理查询结果，选择航班并构建返回数据"""
    # 选择合适的航班
    flight_info = None
    selected_flight_key = f"{dep}-{arr}-{date}"

    if fnb:
        # 如果指定了航班号，查找匹配的航班
        for result_item in search_result['data']['results']:
            trip = result_item['trips'][0]
            if fnb in trip['flight_nos']:
                flight_info = trip
                break
        if not flight_info:
            logger.warning(f'{dep}-{arr}-{date} 未找到指定航班 {fnb}')
            return None
    else:
        # 如果是该ftd第一次遇到且还没有选定航班，则在此时选择最佳航班
        if is_first_time_for_ftd and selected_flight_key not in selected_flights:
            logger.info(f"为 {selected_flight_key} 第一次处理时选择最佳航班（币种：{currency_code}）...")
            best_flight = _select_best_flight_from_results(search_result['data']['results'])
            if best_flight:
                selected_flights[selected_flight_key] = best_flight
                logger.info(f"选定航班: {best_flight}")

        # 使用选定的航班
        target_flight_nos = selected_flights.get(selected_flight_key)
        if target_flight_nos:
            for result_item in search_result['data']['results']:
                trip = result_item['trips'][0]
                if set(trip['flight_nos']) == set(target_flight_nos):
                    flight_info = trip
                    break

        if not flight_info:
            # 如果没找到选定航班，使用第一个航班
            flight_info = search_result['data']['results'][0]['trips'][0]
            logger.warning(f'{dep}-{arr}-{date} 未找到选定航班，使用第一个可用航班')

    book_currency_code = search_result['data']['exchange']['currency_code']

    # 计算基础价格信息
    original_base_price = round(float(flight_info['fares']['adult']['base']), 2)
    original_tax_price = round(float(flight_info['fares']['adult']['tax']), 2)
    original_total_price = round(original_base_price + original_tax_price, 2)

    tmp = {
        '航线': f'{dep}_{arr}_{date}',
        '航班': ','.join(flight_info['flight_nos']),
        '舱位': ','.join(flight_info['cabin_codes']),
        '余票': flight_info['fares']['adult']['quantity'],
        '币种': book_currency_code,
        '原票价': original_base_price,
        '原税费': original_tax_price,
        '原总价': original_total_price,
        # CNY总价将在后续统一处理
    }

    # 计算汇率和人民币价格，暂时不需要
    # if book_currency_code == 'CNY':
    #     tmp['票价(人民币)'] = tmp['原票价']
    #     tmp['税(人民币)'] = tmp['原税费']
    #     tmp['总价(人民币)'] = tmp['原票价'] + tmp['原税费']
    #     tmp['汇率'] = 1
    # else:
    #     try:
    #         resp = pay_center_sdk.send(
    #             request=sdks.pay_center.exchange_rate.GetExchangeRateRequest(
    #                 from_currency=book_currency_code, to_currency="CNY"
    #             )
    #         )
    #         rate = resp['data']['rate']
    #         tmp['票价(人民币)'] = round(tmp['原票价'] * rate, 2)
    #         tmp['税(人民币)'] = round(tmp['原税费'] * rate, 2)
    #         tmp['总价(人民币)'] = round(tmp['票价(人民币)'] + tmp['税(人民币)'], 2)
    #         tmp['汇率'] = round(rate, 6)
    #     except Exception as e:
    #         logger.error(f"获取汇率失败 {book_currency_code} -> CNY: {e}")
    #         tmp['票价(人民币)'] = 0
    #         tmp['税(人民币)'] = 0
    #         tmp['总价(人民币)'] = 0
    #         tmp['汇率'] = 0

    return tmp


def _select_best_flight_from_results(results):
    """从查询结果中选择余票最充足的航班"""
    try:
        if not results:
            return None

        # 找到余票最多的航班
        best_flight = None
        max_quantity = 0

        for result_item in results:
            trip = result_item['trips'][0]
            quantity = trip['fares']['adult']['quantity']
            if quantity > max_quantity:
                max_quantity = quantity
                best_flight = trip['flight_nos']

        return best_flight
    except Exception as e:
        logger.error(f"从结果中选择最佳航班失败: {e}")
        return None


def _load_agent_rates():
    """统一读取代理汇率文件"""
    try:
        import json
        import os

        rate_file_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'statics', 'vj_agent_rate.json'
        )
        with open(rate_file_path, 'r', encoding='utf-8') as f:
            agent_rates = json.load(f)

        logger.info(f"成功读取代理汇率文件，包含 {len(agent_rates)} 个币种")
        return agent_rates
    except Exception as e:
        logger.error(f"读取代理汇率文件失败: {e}")
        return {}


def _add_currency_conversion_columns(results_by_query, agent_rates):
    """在输出Excel前统一添加汇率转换列"""
    for query_key, results in results_by_query.items():
        if not results:
            continue

        for result in results:
            # 提取币种代码（去掉标签部分）
            currency_with_label = result.get('币种', '')
            if '(' in currency_with_label:
                currency_code = currency_with_label.split('(')[0]
            else:
                currency_code = currency_with_label

            original_total_price = result.get('原总价', 0)

            # 计算CNY总价
            if currency_code in agent_rates:
                rate = agent_rates[currency_code]
                cny_total_price = round(original_total_price * rate, 2)
                logger.debug(
                    f"汇率转换: {currency_code} -> CNY, 汇率: {rate}, 原总价: {original_total_price}, CNY总价: {cny_total_price}"
                )
            else:
                cny_total_price = 0
                if currency_code:  # 只有当币种代码不为空时才警告
                    logger.warning(f"代理汇率文件中未找到币种: {currency_code}")

            # 添加CNY总价列
            result['CNY总价'] = cny_total_price


def _format_min_cny_price_row(writer, sheet_name, df):
    """将每个sheet中CNY总价最小的行加粗标红"""
    try:
        from openpyxl.styles import Font

        # 获取工作表
        worksheet = writer.sheets[sheet_name]

        # 检查是否有CNY总价列
        if 'CNY总价' not in df.columns:
            logger.warning(f"Sheet {sheet_name} 中没有找到CNY总价列，跳过格式化")
            return

        # 找到CNY总价最小值（排除0值）
        cny_prices = df['CNY总价'].values
        valid_prices = [price for price in cny_prices if price > 0]

        if not valid_prices:
            logger.warning(f"Sheet {sheet_name} 中没有有效的CNY总价数据，跳过格式化")
            return

        min_price = min(valid_prices)

        # 找到最小价格对应的行索引（可能有多行）
        min_price_rows = []
        for idx, price in enumerate(cny_prices):
            if price == min_price:
                min_price_rows.append(idx + 2)  # +2因为openpyxl使用1基索引且有表头

        # 定义格式：红色字体+加粗
        red_bold_font = Font(color="FF0000", bold=True)

        # 对最小价格的行进行格式化
        for row_idx in min_price_rows:
            # 格式化整行
            for col_idx in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=row_idx, column=col_idx)
                cell.font = red_bold_font

        logger.info(f"Sheet {sheet_name}: CNY总价最小值 {min_price} 的第 {min_price_rows} 行已加粗标红")

    except Exception as e:
        logger.error(f"格式化Sheet {sheet_name}时出错: {e}")


def _format_summary_sheet_by_query(writer, sheet_name, df):
    """为总汇总sheet按查询条件分组标红最小CNY总价"""
    try:
        from openpyxl.styles import Font

        # 获取工作表
        worksheet = writer.sheets[sheet_name]

        # 检查是否有必要的列
        if 'CNY总价' not in df.columns or '航线' not in df.columns:
            logger.warning(f"Sheet {sheet_name} 中缺少必要的列（CNY总价或航线），跳过格式化")
            return

        # 按航线分组找到每组的最小CNY总价
        min_price_rows = []
        grouped = df.groupby('航线')

        for route, group in grouped:
            # 找到该航线组内CNY总价最小值（排除0值）
            valid_prices = group[group['CNY总价'] > 0]['CNY总价']

            if len(valid_prices) == 0:
                continue

            min_price = valid_prices.min()

            # 找到该组内最小价格对应的原始DataFrame索引
            min_indices = group[group['CNY总价'] == min_price].index.tolist()

            # 转换为Excel行号（+2因为openpyxl使用1基索引且有表头）
            for idx in min_indices:
                excel_row = df.index.get_loc(idx) + 2
                min_price_rows.append(excel_row)

            logger.debug(
                f"航线 {route}: CNY总价最小值 {min_price}，对应Excel行号 {[df.index.get_loc(idx) + 2 for idx in min_indices]}"
            )

        # 定义格式：红色字体+加粗
        red_bold_font = Font(color="FF0000", bold=True)

        # 对最小价格的行进行格式化
        for row_idx in min_price_rows:
            # 格式化整行
            for col_idx in range(1, len(df.columns) + 1):
                cell = worksheet.cell(row=row_idx, column=col_idx)
                cell.font = red_bold_font

        logger.info(f"总汇总sheet: 按查询条件标红了 {len(min_price_rows)} 行（每个查询条件下的最小CNY总价）")

    except Exception as e:
        logger.error(f"格式化总汇总Sheet时出错: {e}")


def fix_quantity(vj_result, vz_result):
    """
    修正VJ的余票数量
    """
    logger.debug(f'vj_result: {vj_result}')
    logger.debug(f'vz_result: {vz_result}')
    for vj_trip in vj_result['data']['results']:
        for vz_trip in vz_result['data']['results']:
            pass
