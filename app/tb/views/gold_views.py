from datetime import datetime, timedelta
import os
import time
from fastapi import APIRouter, Body, Depends, HTTPException, Request, Response
from fastapi.exceptions import RequestValidationError
from fastapi.responses import PlainTextResponse
from loguru import logger
import orjson
from pydantic import BaseModel

from app.config import settings, _global_alert_ext_msg


from app.tb.consts.api_codes import TBOrderCreateApiCodes, TBOrderPayVerifyApiCodes, TBPVerifyApiCodes, TBSearchApiCodes
from app.tb.models.tb_temp_order import TBTempOrder
from app.tb.services import fare_service, msg_service, temp_order_service
from app.tb.utils import CipherUtils
from app.tb.views.schemas import public_schemas
from commons.consts.api_codes import ApiCodes
from commons.depends import get_real_client_ip
from commons.fastapi.schemas import common_schemas


routers = APIRouter(prefix=f'{settings.API_PERFIX}/tb/gold', tags=['TB，金牌产品接口'])


# 普通接口
@routers.post("/search", summary="报价查询")
async def search(item: public_schemas.SearchIn, client_ip: str = Depends(get_real_client_ip)):
    result = None
    if item.tripType != 1:
        # todo 暂时只做单程
        return TBSearchApiCodes.NO_ROUTING.generate_api_result()
    if item.cid != settings.TB_CID:
        return TBSearchApiCodes.PARAM_ERROR.generate_api_result(ext_msg='cid不正确')
    try:
        result = await fare_service.search(item=item, channel_code='tb_gold')
        result = fare_service.format_search_result(result)
    except Exception as e:
        logger.exception(e)
        return TBSearchApiCodes.EXCEPT_ERROR.generate_api_result()
    if not result:
        return TBSearchApiCodes.NO_ROUTING.generate_api_result()
    return TBSearchApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post("/verify", summary="验价")
async def verify(request: Request, item: public_schemas.VerifyIn, client_ip: str = Depends(get_real_client_ip)):
    response = None
    start_time = datetime.now()
    if item.cid != settings.TB_CID:
        response = TBPVerifyApiCodes.EXCEPT_ERROR.generate_api_result(ext_msg='cid不正确')

    elif item.tripType != 1:
        # todo 暂时只做单程
        response = TBPVerifyApiCodes.NO_RESULT.generate_api_result()
    else:
        try:
            result = await fare_service.verify(item=item, start_time=start_time)
            result = await fare_service.parse_verify(result)
            if result:
                response = TBPVerifyApiCodes.SUCCESS.generate_api_result(data=result)
            else:
                response = TBPVerifyApiCodes.NO_RESULT.generate_api_result()
        except Exception as e:
            if len(e.args) > 1:
                response = TBPVerifyApiCodes.generate_result(status=e.args[0], msg=e.args[1])
            else:
                logger.exception(e)
                response = TBPVerifyApiCodes.EXCEPT_ERROR.generate_api_result()

    await msg_service.verify_alert(
        channel_name='金牌',
        step_name='验价1',
        status=response.get('msg'),
        request_id=request.state.request_id,
        flight_key=f'{item.routing["fromSegments"][0]["depAirport"]}-{item.routing["fromSegments"][0]["arrAirport"]}-{item.routing["fromSegments"][0]["flightNumber"]}-{item.routing["fromSegments"][0]["depTime"][:8]}',
        session_id=response.get('sessionId', ''),
    )
    try:
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name='platform_api',
            api_type='tb_gold',
            api_path='verify',
            airline_code=item.routing['fromSegments'][0]['flightNumber'][0:2],
            dep_airport_code=item.routing['fromSegments'][0]['depAirport'],
            arr_airport_code=item.routing['fromSegments'][0]['arrAirport'],
            dep_date=item.routing['fromSegments'][0]['depTime'][:8],
            flight_no=item.routing['fromSegments'][0]['flightNumber'],
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            cost_time=(datetime.now() - start_time).total_seconds(),
            code=response.get('code', -1),
            message=response.get('message', ''),
        )
    except Exception as e:
        logger.exception(e)
    return response


@routers.post("/order/create", summary="订单创建", response_class=PlainTextResponse)
async def order_create(request: Request, body: str = Body(...), client_ip: str = Depends(get_real_client_ip)):
    result = None
    params = None
    start_time = datetime.now()
    try:
        params = CipherUtils.aes_decrypt(settings.TB_AES_KEY, body)

        params = orjson.loads(params)

        item = public_schemas.OrderCreateIn(**params)
        if item.cid != settings.TB_CID:
            _global_alert_ext_msg.set(f'cid不正确，cid:{item.cid}')
            logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')
            result = TBOrderCreateApiCodes.NO_RESULT.generate_api_result(ext_msg='cid不正确')
        elif item.tripType != 1:
            # todo 暂时只做单程
            _global_alert_ext_msg.set(f'tripType不正确，tripType:{item.tripType}')
            logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')
            result = TBOrderCreateApiCodes.NO_RESULT.generate_api_result()
        else:
            # 正常流程
            await fare_service.order_check(item=item)
            result = await fare_service.create_order(item=item, fare_type='tb_gold')
            result = await fare_service.parse_create_order(
                result=result, item=item, fare_type='tb_gold', start_time=start_time
            )

            result = TBOrderCreateApiCodes.SUCCESS.generate_api_result(data=result)
            await temp_order_service.save_temp_order(item=item, result=result)
            logger.info(result)
    except Exception as e:
        if len(e.args) > 1:
            result = TBOrderCreateApiCodes.generate_result(status=e.args[0], msg=e.args[1])
            logger.warning(f'生单失败,业务异常 {result}')
        else:
            logger.exception(e)
            result = TBOrderCreateApiCodes.NO_RESULT.generate_api_result(ext_msg=str(e))
            logger.warning(f'生单失败,系统异常 {result}')
    if not result:
        result = TBOrderCreateApiCodes.NO_RESULT.generate_api_result()
    await msg_service.verify_alert(
        channel_name='金牌',
        step_name='生单（验价2）',
        status=result.get('msg'),
        request_id=request.state.request_id,
        flight_key=f'{params["routing"]["fromSegments"][0]["depAirport"]}-{params["routing"]["fromSegments"][0]["arrAirport"]}-{params["routing"]["fromSegments"][0]["flightNumber"]}-{params["routing"]["fromSegments"][0]["depTime"][:8]}',
        session_id=params.get('sessionId', ''),
    )
    try:
        # logger.bind(write_tag="api_es_log").info(
        #     '',
        #     api_type="receive",
        #     api_path=request.url.path,
        #     request=orjson.dumps(params).decode('utf-8'),
        #     response=orjson.dumps(result).decode('utf-8'),
        #     status='success' if result.get('status', -1) == 0 else 'fail',
        #     code=result.get('status', -1),
        #     message=result.get('msg', ''),
        #     cost_time=time.time() - start_time,
        # )
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name='platform_api',
            api_type='tb_gold',
            api_path='order_create',
            airline_code=params["routing"]["fromSegments"][0]["flightNumber"][0:2],
            dep_airport_code=params['routing']['fromSegments'][0]['depAirport'],
            arr_airport_code=params['routing']['fromSegments'][0]['arrAirport'],
            dep_date=params['routing']['fromSegments'][0]['depTime'][:8],
            flight_no=params['routing']['fromSegments'][0]['flightNumber'],
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            cost_time=(datetime.now() - start_time).total_seconds(),
            code=result.get('status', -1),
            message=result.get('msg', ''),
        )
    except Exception as e:
        logger.exception(e)
    encrypted = CipherUtils.aes_encrypt(settings.TB_AES_KEY, orjson.dumps(result).decode('utf-8'))
    return Response(content=encrypted)


@routers.post('/order/cancel', summary='取消订单')
async def order_cancel(item: public_schemas.OrderCancelIn, client_ip: str = Depends(get_real_client_ip)):
    result = {"sessionId": item.sessionId}
    await TBTempOrder.update_by_async(TBTempOrder.order_no == item.orderNo, TBTempOrder.canceled == 0, canceled=1)
    # todo 这里暂时什么都不用做
    return TBOrderCreateApiCodes.SUCCESS.generate_api_result(data=result)


@routers.post('/order/pay_verify', summary='支付前校验', response_class=PlainTextResponse)
async def order_pay_verify(request: Request, body: str = Body(...), client_ip: str = Depends(get_real_client_ip)):
    result = None
    params = None
    start_time = datetime.now()
    try:
        params = CipherUtils.aes_decrypt(settings.TB_AES_KEY, body)
        params = orjson.loads(params)

        item = public_schemas.OrderPayVerifyIn(**params)
        if item.cid != settings.TB_CID:
            _global_alert_ext_msg.set(f'cid不正确，cid:{item.cid}')
            result = TBOrderPayVerifyApiCodes.EXCEPT_ERROR.generate_api_result(ext_msg='cid不正确')
        elif item.tripType != 1:
            # todo 暂时只做单程
            _global_alert_ext_msg.set(f'tripType不正确，tripType:{item.tripType}')
            result = TBOrderPayVerifyApiCodes.NO_RESULT.generate_api_result()
        else:
            result = await fare_service.order_pay_verify_real(item=item, fare_type='tb_gold')
            # 正常流程
            if not result:
                result = await fare_service.order_pay_verify(item=item, fare_type='tb_gold')
            result = await fare_service.parse_pay_verify_result(result=result, item=item)
            result = TBOrderPayVerifyApiCodes.SUCCESS.generate_api_result(data=result)

    except Exception as e:
        if len(e.args) > 1:
            result = TBOrderPayVerifyApiCodes.generate_result(status=e.args[0], msg=e.args[1])
            logger.warning(f'支付前校验,业务异常 {result}')

        else:
            logger.exception(e)
            result = TBOrderPayVerifyApiCodes.EXCEPT_ERROR.generate_api_result(ext_msg=str(e))
            logger.warning(f'支付前校验,系统异常 {result}')

    if not result:
        result = TBOrderPayVerifyApiCodes.NO_RESULT.generate_api_result()
    await msg_service.verify_alert(
        channel_name='金牌',
        step_name='支付前校验（验价3）',
        status=result.get('msg'),
        request_id=request.state.request_id,
        flight_key=f'{params["routing"]["fromSegments"][0]["depAirport"]}-{params["routing"]["fromSegments"][0]["arrAirport"]}-{params["routing"]["fromSegments"][0]["flightNumber"]}-{params["routing"]["fromSegments"][0]["depTime"][:8]}',
        session_id=params.get('sessionId', ''),
    )
    try:
        # logger.bind(write_tag="api_es_log").info(
        #     '',
        #     api_type="receive",
        #     api_path=request.url.path,
        #     request=orjson.dumps(params).decode('utf-8'),
        #     response=orjson.dumps(result).decode('utf-8'),
        #     status='success' if result.get('status', -1) == 0 else 'fail',
        #     code=result.get('status', -1),
        #     message=result.get('msg', ''),
        #     cost_time=time.time() - start_time,
        # )
        logger.bind(write_tag="elasticsearch").info(
            '',
            server_name='platform_api',
            api_type='tb_gold',
            api_path='pay_verify',
            airline_code=params["routing"]["fromSegments"][0]["flightNumber"][0:2],
            dep_airport_code=params['routing']['fromSegments'][0]['depAirport'],
            arr_airport_code=params['routing']['fromSegments'][0]['arrAirport'],
            dep_date=params['routing']['fromSegments'][0]['depTime'][:8],
            flight_no=params['routing']['fromSegments'][0]['flightNumber'],
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            cost_time=(datetime.now() - start_time).total_seconds(),
            code=result.get('status', -1),
            message=result.get('msg', ''),
        )
    except Exception as e:
        logger.exception(e)
    encrypted = CipherUtils.aes_encrypt(settings.TB_AES_KEY, orjson.dumps(result).decode('utf-8'))
    return Response(content=encrypted)
