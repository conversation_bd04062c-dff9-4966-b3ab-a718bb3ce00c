import asyncio
import copy
from datetime import datetime, timedelta
from math import ceil
import random
import time
from typing import Union

from loguru import logger
import orj<PERSON>
from app.sdks.flight_fare_sdk import FlightFareSdk
from app.tb.consts.api_codes import (
    OrderCreateVerifyToTBCodes,
    OrderPayVerifyToTBCodes,
    TBOrderCreateApiCodes,
    TBOrderPayVerifyApiCodes,
    TBPVerifyApiCodes,
    VerifyToTBCodes,
)
from app.tb.consts.types import AuxType, CardType, PassengerType, SexType
from app.tb.models.tb_temp_order import TBTempOrder
from app.tb.services import temp_order_service
from app.tb.views.schemas import public_schemas
from app.config import settings, _global_alert_ext_msg, price_floating_rules
from commons.consts.api_codes import ApiCodes
from app.depends import redis_pool
import commons.sdks as hy_sdks
from commons.sdks.core import get_sub_sdk


async def search(item: public_schemas.SearchIn, channel_code: str):
    search_request = hy_sdks.flight_fare.search.FlightSearchRequest(
        channel_code=channel_code,
        dep_city_code=item.fromCity,
        arr_city_code=item.toCity,
        dep_date=datetime.strptime(item.fromDate, '%Y%m%d').strftime('%Y-%m-%d'),
    )
    fare_sdk = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    # result = fare_sdk.send(request=search_request)
    result = await fare_sdk.send_async(request=search_request)

    logger.debug(result)
    return result


def format_search_result(result):
    # todo 缓存先固定为5分钟
    fmt_result = {"validTime": settings.TB_DEFAULT_VALID_TIME, "routings": []}
    routings = []
    valid_time = 0
    tmp_product_list = {}
    for flight_info in result['data']:
        if not valid_time or (
            flight_info['flight_info'].get('expire_seconds')
            and flight_info['flight_info']['expire_seconds'] < valid_time
        ):
            valid_time = flight_info['flight_info']['expire_seconds']
        airline_code = flight_info['flight_info']['airline_code']
        for p in flight_info['products']:
            product = update_floating_rule(airline_code=airline_code, product=p)
            bidding_range, product = get_bidding_range(airline_code=airline_code, product=product)
            adult_price = product['adult']['base']
            adult_tax = product['adult']['tax']

            routing_key = f'{flight_info["flight_info"]["flight_no"]}_{product["cabin"]}'
            # 跳过舱位相同但价格高的产品
            if routing_key in tmp_product_list:
                old_total = tmp_product_list[routing_key]['adultPrice'] + tmp_product_list[routing_key]['adultTax']
                if old_total < (adult_price + adult_tax):
                    continue
            routing = {
                "data": product['fare_key'],
                "currency": "CNY",
                "adultPrice": adult_price,
                "biddingRange": bidding_range,
                "adultTax": adult_tax,
                # 不销售时隐藏字段
                # "childPrice": child_price,
                # "childTax": child_tax,
                # "infantPrice": infant_price,
                # "infantTax": infant_tax,
                "isCombine": 0,  # 非拼接报价
                "nationalityType": 0,  # 不限乘客国籍
                "nationality": "",  # 不限乘客国籍
                "suitAge": "",  # 不限制年龄
                "priceType": 0,  # 报价类型：0 普通价 / 1 留学生价 /3-劳工/4-新移民/5-海员/6-老人/7-青年
                "applyType": 0,  # 默认0
                "adultTaxType": 0,  # 成人税费类型：0 未含税 / 1 已含税
                "childTaxType": 0,  # 儿童税费类型：0 未含税 / 1 已含税
                "minPassengerCount": 1,
                "maxPassengerCount": 9,
                "gvChildRule": 1,
                "invoiceType": 3,  # todo
                "Ataiff": "",
                "isUseFliggyRule": 0,
                "fareFrom": "PUB",
                "priceSource": "AW",  # 运价渠道：,1E：TravelSky,1A：Amadeus,1B：Abacus,1S：Sabre,1P：WorldSpan,1G：Galileo,AW：航司官网渠道 Airline Website,AI：航司直连接口 Airline Interface,非必传。如非空且不符合如上规则，则此字段内容降级为空。
                "salesPoint": "",
                "rules": [
                    {
                        "isAirFareRule": 0,
                        "hasRefund": 0,  # 不允许退票
                        "refundCurrency": "CNY",
                        "refund": "*-0-*",
                        "refundTax": "0",
                        "partRefund": 0,  # 部分未使用，也不允许退票
                        "partRefundCurrency": "CNY",
                        "partRefundPrice": "*-0-*",
                        "partRefundTax": "0",
                        "partRefundSeg": "ALL",
                        "hasEndorse": 0,  # 不允许改期
                        "endorseCurrency": "CNY",
                        "endorse": "*-0-*",
                        "partEndorse": 0,
                        "partEndorseCurrency": "",
                        "partEndorsePrice": "",
                        "endorsement": 0,
                        "hasBaggage": 0,
                        "baggage": "以航空公司规定为准",
                        # 误机罚金
                        "hasNoShow": 0,
                        "noShowLimitTime": 0,
                        "noShowCurrency": "CNY",
                        "penalty": "100%",
                        "specialNoShow": 0,
                        "other": "",
                        "passengerType": 0,
                    }
                ],
                "fromSegments": [],
                "retSegments": [],
            }

            if 'child' in product and product['child']['base'] > 0:
                routing['childPrice'] = product['child']['base']
                routing['childTax'] = product['child']['tax']
                child_rule = copy.deepcopy(routing['rules'][0])
                child_rule['passengerType'] = 1
                routing['rules'].append(child_rule)
            if 'infant' in product and product['infant']['base'] > 0:
                routing['infantPrice'] = product['infant']['base']
                routing['infantTax'] = flight_info['products'][0]['infant']['tax']
                infant_rule = copy.deepcopy(routing['rules'][0])
                infant_rule['passengerType'] = 2
                routing['rules'].append(infant_rule)
            for segment in flight_info['flight_info']['segments']:
                dep_time = datetime.strptime(f'{segment["dep_date"]} {segment["dep_time"]}', '%Y-%m-%d %H:%M').strftime(
                    '%Y%m%d%H%M'
                )
                arr_time = datetime.strptime(f'{segment["arr_date"]} {segment["arr_time"]}', '%Y-%m-%d %H:%M').strftime(
                    '%Y%m%d%H%M'
                )
                max_seats = product['adult']['quantity']
                if max_seats > 9:
                    max_seats = 9
                from_segment = {
                    "carrier": segment['airline_code'],
                    "flightNumber": segment['flight_no'],
                    "depAirport": segment['dep_airport_code'],
                    "depTime": dep_time,
                    "arrAirport": segment['arr_airport_code'],
                    "arrTime": arr_time,
                    "stopCities": "",
                    "codeShare": segment['share_code'],
                    "operatingCarrier": "",
                    "operatingFlightNo": "",
                    "departureTerminal": "",
                    "arrivingTerminal": "",
                    "cabin": product['cabin'],
                    "aircraftCode": segment['aircraft_code'],
                    "seatCount": max_seats,
                    "cabinClass": "Y",
                    "fareTypeCode": "",
                    "fareBasis": "",
                    "ruleNo": "",
                    "baggages": [],
                }
                # 只判断托运行李
                if product.get('includes', {}).get('baggage', {}).get('checked_baggage'):
                    for baggage in product['includes']['baggage']['checked_baggage']:
                        baggage_element = {
                            "weight": baggage['weight'],
                            "pc": baggage['count'],
                            "isAllWeight": True,
                            "passengerType": 0,
                        }

                        # 新版接口用baggages
                        from_segment['baggages'].append(baggage_element)
                # 这个if是配合平台测试，完成问题修改后需要改回来
                if product.get('child_includes', {}).get('baggage', {}).get('checked_baggage'):
                    for baggage in product['child_includes']['baggage']['checked_baggage']:
                        baggage_element = {
                            "weight": baggage['weight'],
                            "pc": baggage['count'],
                            "isAllWeight": True,
                            "passengerType": 1,
                        }

                        # 新版接口用baggages
                        from_segment['baggages'].append(baggage_element)

                if product.get('infant_includes', {}).get('baggage', {}).get('checked_baggage'):
                    for baggage in product['infant_includes']['baggage']['checked_baggage']:
                        baggage_element = {
                            "weight": baggage['weight'],
                            "pc": baggage['count'],
                            "isAllWeight": True,
                            "passengerType": 2,
                        }
                        # 新版接口用baggages
                        from_segment['baggages'].append(baggage_element)

                routing['fromSegments'].append(from_segment)
                tmp_product_list[routing_key] = routing
    if tmp_product_list:
        routings = list(tmp_product_list.values())
        fmt_result['validTime'] = valid_time
    fmt_result["routings"] = routings
    return fmt_result


# async def remove_locked_product(trip_info: dict, total_passenger: int = 0):
#     tmp_flight_info = copy.deepcopy(trip_info)
#     del tmp_flight_info['products']
#     tmp_flight_info['products'] = []
#     for product in trip_info['products']:
#         lock_count = await temp_order_service.get_locked_num(
#             dep_airport_code=trip_info['flight_info']['dep_airport_code'],
#             arr_airport_code=trip_info['flight_info']['arr_airport_code'],
#             dep_date=trip_info['flight_info']['dep_date'],
#             flight_no=trip_info['flight_info']['flight_no'],
#             cabin_class=product['cabin_class'],
#             src_base=product['adult']['src_base'],
#             src_currency=product['adult']['src_currency'],
#         )

#         # 扣除锁定人数
#         if lock_count > 0:
#             actual_count = product['adult']['quantity'] - lock_count
#             if total_passenger > 0:
#                 if actual_count < total_passenger:
#                     logger.info(
#                         f'删除临时锁定人数（{lock_count}）后，剩余人数：{actual_count},小于总人数：{total_passenger}'
#                     )
#                     continue

#             product['adult']['quantity'] = actual_count
#             logger.info(f'删除临时锁定人数（{lock_count}）后，剩余人数：{actual_count}')

#             # 判断儿童、婴儿数量
#             if 'child' in product:
#                 if product['adult']['quantity'] == 1:
#                     product['child']['quantity'] = 0
#                 else:
#                     product['child']['quantity'] = product['child']['quantity'] - lock_count
#                 logger.info(f'删除后儿童余票：{product["child"]["quantity"]}')
#             if 'infant' in product:
#                 if product['adult']['quantity'] == 1:
#                     product['infant']['quantity'] = 0
#                 else:
#                     product['infant']['quantity'] = product['infant']['quantity'] - lock_count
#                 logger.info(f'删除后婴儿余票：{product["infant"]["quantity"]}')
#         # 判断余票
#         if product['adult']['quantity'] > 0:
#             tmp_flight_info['products'].append(product)
#         else:
#             logger.warning(f'航班余票不足{0}，航班信息：{trip_info["flight_info"]}')
#     logger.debug(f'tmp_flight_info:{tmp_flight_info}')
#     return tmp_flight_info


async def verify(item: public_schemas.VerifyIn, start_time: datetime = None):

    fare_sdk = get_sub_sdk(module_name='flight_fare', host=settings.FLIGHT_FARE_URL)

    # 标记是否需要走缓存逻辑
    need_cache_verify = False

    for _ in range(settings.TB_VERIFY_LOOP_TIMES):
        # 检查是否超时
        if start_time and (datetime.now() - start_time).total_seconds() >= settings.TB_VERIFY_TIMEOUT:
            logger.warning(
                f'验价接口超时，已执行时间：{(datetime.now() - start_time).total_seconds()}秒，强制退出循环走缓存逻辑'
            )
            need_cache_verify = True
            break

        result = await fare_sdk.flight_real_time_verify_async(
            fare_key=item.routing['data'],
            dep_airport_code=item.routing['fromSegments'][0]['depAirport'],
            arr_airport_code=item.routing['fromSegments'][0]['arrAirport'],
            flight_no=item.routing['fromSegments'][0]['flightNumber'],
            adult=1,
            child=0,
            infant=0,
            keep_time=settings.TB_TEMP_ORDER_LOCK_TIME * 60,
        )

        logger.debug(result)
        # 验价成功
        if result['code'] == ApiCodes.SUCCESS.value:
            if 'data' in result:
                return result
            else:
                _global_alert_ext_msg.set('接口无数据')
                TBOrderPayVerifyApiCodes.NO_RESULT.raise_error()
        # 继续等待
        if result['code'] == ApiCodes.TASK_RUNNING.value:
            await asyncio.sleep(1)
            continue
        else:
            break
    else:
        # 正常循环结束，需要走缓存逻辑
        need_cache_verify = True

    # 实时校验无返回时或超时时改用普通（缓存）验价
    if need_cache_verify:
        # if result['code'] in OrderCreateVerifyToTBCodes:
        #     tb_code = OrderPayVerifyToTBCodes.get(result['code'], TBPVerifyApiCodes.EXCEPT_ERROR)
        #     _global_alert_ext_msg.set(f'运价查询（single）接口报错 code:{result["code"]} msg:{result["message"]}')
        #     tb_code.raise_error(ext_msg=result['message'])

        result = await fare_sdk.flight_verify_async(
            fare_key=item.routing['data'],
            dep_airport_code=item.routing['fromSegments'][0]['depAirport'],
            arr_airport_code=item.routing['fromSegments'][0]['arrAirport'],
            flight_no=item.routing['fromSegments'][0]['flightNumber'],
        )
    return result


def update_floating_rule(airline_code: str, product: dict):
    tb_min_base_price = price_floating_rules.get('taobao.min_base_price', 1)
    cfg = price_floating_rules.get(f'taobao.airlines.{airline_code}')
    if cfg:
        if cfg.get('allow_base_to_tax', False):
            for k in ['adult', 'child', 'infant']:
                if k not in product:
                    continue
                tag_price = ceil(product[k]['base'] * cfg.get('base_to_tax_rate', 0))
                if product[k]['base'] - tag_price > tb_min_base_price:
                    product[k]['base'] = product[k]['base'] - tag_price
                    product[k]['tax'] = product[k]['tax'] + tag_price
                    logger.info(
                        f'{airline_code} 票面向税费转移规则：{cfg}，转移金额：{tag_price}，转移后票面价：{product["adult"]["base"]}(原票面价：{product["adult"]["src_cny_base"]})，转移后税费：{product["adult"]["tax"]}(原税费：{product["adult"]["src_cny_tax"]})'
                    )
                else:
                    logger.warning(
                        f'{airline_code} 票面向税费转移规则：{cfg}，转移金额：{tag_price}，低于最低票面价：{tb_min_base_price}，停止转移'
                    )
    return product


def fix_base(product_fare: dict, tb_min_base_price: float, bidding_range: float = 0):
    # 浮动后的票价
    now_base = product_fare['base']
    if bidding_range > 0:
        # 如果有竞价空间，则按扣除竞价空间后的票价计算
        now_base = now_base - bidding_range
    # 如果浮动后的票价低于最低票面价
    if now_base < tb_min_base_price:
        # 计算需要补齐的票面价
        diff = tb_min_base_price - now_base
        logger.info(f'票面{now_base} 小于最低票面价{tb_min_base_price}，需要补齐的票面价: {diff}')
        # 补齐票面价
        product_fare['base'] = product_fare['base'] + diff
        # 补齐税费
        product_fare['tax'] = product_fare['tax'] - diff
        logger.info(
            f'补齐后票价: {product_fare["base"]} 补齐后税费: {product_fare["tax"]} 补齐后总价: {product_fare["total"]}'
        )

    return product_fare


def get_bidding_range(airline_code: str, product: dict):
    new_product = copy.deepcopy(product)
    bidding_range = 0
    enable_bidding_range = price_floating_rules.get('taobao.enable_bidding_range', False)
    if new_product.get('enable_auto_float', False):
        tb_min_base_price = price_floating_rules.get('taobao.min_base_price', 1)
        cfg = price_floating_rules.get(f'taobao.airlines.{airline_code}')

        actual_float = new_product.get('actual_float', 0)
        max_float = new_product.get('max_float', 0)
        min_float = new_product.get('min_float', 0)

        for k, v in new_product.items():
            if k not in ['adult', 'child', 'infant']:
                continue

            if enable_bidding_range:
                bidding_range = max_float - min_float
                logger.info(f'开启竞价空间: {bidding_range}')
                if cfg.get('allow_base_to_tax') is True:
                    # 如果允许将票价转移到税费，则只浮动税费
                    new_product[k]['tax'] = new_product[k]['tax'] + max_float
                    new_product[k]['total'] = new_product[k]['total'] + max_float
                    logger.info(
                        f'在税费上浮动: {max_float}，浮动后税费: {new_product[k]["tax"]}, 票面价: {new_product[k]["base"]}, 总价: {new_product[k]["total"]}'
                    )
                else:
                    # 如果不允许将票价转移到税费，则只浮动票价
                    # 避免退税损失风险
                    new_product[k]['base'] = new_product[k]['base'] + max_float
                    new_product[k]['total'] = new_product[k]['total'] + max_float
                    logger.info(
                        f'在票价上浮动: {max_float}，浮动后票价: {new_product[k]["base"]}, 税费: {new_product[k]["tax"]}, 总价: {new_product[k]["total"]}'
                    )
            else:
                bidding_range = 0
                logger.info(f'关闭竞价空间')
                if cfg.get('allow_base_to_tax') is True:
                    # 如果允许将票价转移到税费，则只浮动税费
                    new_product[k]['tax'] = new_product[k]['tax'] + actual_float
                    new_product[k]['total'] = new_product[k]['total'] + actual_float
                    logger.info(
                        f'在税费上浮动: {actual_float}，浮动后税费: {new_product[k]["tax"]}, 票面价: {new_product[k]["base"]}, 总价: {new_product[k]["total"]}'
                    )
                else:
                    # 如果不允许将票价转移到税费，则只浮动票价
                    # 避免退税损失风险
                    new_product[k]['base'] = new_product[k]['base'] + actual_float
                    new_product[k]['total'] = new_product[k]['total'] + actual_float
                    logger.info(
                        f'在票价上浮动: {actual_float}，浮动后票价: {new_product[k]["base"]}, 税费: {new_product[k]["tax"]}, 总价: {new_product[k]["total"]}'
                    )

            new_product[k] = fix_base(
                product_fare=new_product[k], tb_min_base_price=tb_min_base_price, bidding_range=bidding_range
            )
            logger.info(
                f'最终票面价: {new_product[k]["base"]} 最终税费: {new_product[k]["tax"]} 最终总价: {new_product[k]["total"]}'
            )
    return bidding_range, new_product


def format_verify_result(result):
    flight_info = result['data']['flight_info']
    old_product = result['data']['products'][0]
    airline_code = flight_info['airline_code']
    product = update_floating_rule(airline_code=airline_code, product=old_product)
    bidding_range, product = get_bidding_range(airline_code=airline_code, product=product)
    max_seats = product['adult']['quantity']
    if max_seats > 9:
        max_seats = 9
    elif max_seats == 0:
        TBPVerifyApiCodes.TICKET_NOT_ENOUGH.raise_error(ext_msg=result['message'])

    dep_time = datetime.strptime(f'{flight_info["dep_date"]} {flight_info["dep_time"]}', '%Y-%m-%d %H:%M').strftime(
        '%Y%m%d%H%M'
    )
    arr_time = datetime.strptime(f'{flight_info["arr_date"]} {flight_info["arr_time"]}', '%Y-%m-%d %H:%M').strftime(
        '%Y%m%d%H%M'
    )
    fmt_result = {
        # "sessionId": result['data']['session_id'],
        "maxSeats": max_seats,
        "routing": {
            "data": product['fare_key'],
            "currency": "CNY",
            "adultPrice": product['adult']['base'],
            "biddingRange": bidding_range,
            "adultTax": product['adult']['tax'],
            # "childPrice": 0,
            # "childTax": 0,
            # "infantPrice": 0,
            # "infantTax": 0,
            "nationalityType": 0,
            "nationality": "",
            "suitAge": "",
            "priceType": 0,
            "applyType": 0,
            "adultTaxType": 0,
            "childTaxType": 0,
            "minPassengerCount": 1,
            "maxPassengerCount": 9,
            "gvChildRule": 1,
            "fromSegments": [
                {
                    "carrier": flight_info['airline_code'],
                    "flightNumber": flight_info['flight_no'],
                    "depAirport": flight_info['dep_airport_code'],
                    "depTime": dep_time,
                    "arrAirport": flight_info['arr_airport_code'],
                    "arrTime": arr_time,
                    "stopCities": "",
                    "codeShare": flight_info['segments'][0]['share_code'],
                    "operatingCarrier": "",
                    "operatingFlightNo": "",
                    "departureTerminal": "",
                    "arrivingTerminal": "",
                    "cabin": product['cabin'],
                    "aircraftCode": flight_info['segments'][0]['aircraft_code'],
                    "seatCount": max_seats,
                    "cabinClass": product['cabin_class'],
                }
            ],
            "retSegments": [],
        },
        "rules": [
            {
                "isAirFareRule": 0,
                "hasRefund": 0,  # 不允许退票
                "refundCurrency": "CNY",
                "refund": "*-0-*",
                "refundTax": "0",
                "partRefund": 0,  # 部分未使用，也不允许退票
                "partRefundCurrency": "CNY",
                "partRefundPrice": "*-0-*",
                "partRefundTax": "0",
                "partRefundSeg": "ALL",
                "hasEndorse": 0,  # 不允许改期
                "endorseCurrency": "CNY",
                "endorse": "*-0-*",
                "partEndorse": 0,
                "partEndorseCurrency": "",
                "partEndorsePrice": "",
                "endorsement": 0,
                "hasBaggage": 0,
                "baggage": "以航空公司规定为准",
                # 误机罚金
                "hasNoShow": 0,
                "noShowLimitTime": 0,
                "noShowCurrency": "CNY",
                "penalty": "100%",
                "specialNoShow": 0,
                "other": "",
                "passengerType": 0,
            }
        ],
    }
    if 'child' in product and product['child']['base'] > 0 and product['child']['quantity'] > 0:
        fmt_result['routing']['childPrice'] = product['child']['base']
        fmt_result['routing']['childTax'] = product['child']['tax']
        child_rule = copy.deepcopy(fmt_result['rules'][0])
        child_rule['passengerType'] = 1
        fmt_result['rules'].append(child_rule)
    if 'infant' in product and product['infant']['base'] > 0 and product['infant']['quantity'] > 0:
        fmt_result['routing']['infantPrice'] = product['infant']['base']
        fmt_result['routing']['infantTax'] = product['infant']['tax']
        infant_rule = copy.deepcopy(fmt_result['rules'][0])
        infant_rule['passengerType'] = 2
        fmt_result['rules'].append(infant_rule)

    return fmt_result


async def parse_verify(result):
    if result['code'] != ApiCodes.SUCCESS.value:
        logger.error(result)
        tb_code = VerifyToTBCodes.get(result['code'], TBPVerifyApiCodes.EXCEPT_ERROR)
        _global_alert_ext_msg.set(f'运价查询（single）接口报错 code:{result["code"]} msg:{result["message"]}')
        tb_code.raise_error(ext_msg=result['message'])
    if not result.get('data'):
        _global_alert_ext_msg.set('运价查询（single）接口无数据')
        TBPVerifyApiCodes.NO_RESULT.raise_error()

    session_id = await set_verify_session(result=result['data'])
    # 锁单逻辑验价拦截
    # 此逻辑改由运价实现 2025-04-07
    # result['data'] = await remove_locked_product(trip_info=result['data'])
    # if not result['data'] or not result['data']['products']:
    #     logger.warning(f'删除锁单后无票，trip_info:{result}')
    #     _global_alert_ext_msg.set('扣除锁定订单后无票')
    #     TBPVerifyApiCodes.TICKET_NOT_ENOUGH.raise_error(ext_msg=result['message'])

    fmt_result = format_verify_result(result)
    fmt_result['sessionId'] = session_id
    return fmt_result


async def order_check(item: public_schemas.OrderCreateIn):
    # 检查非护照乘客
    # no_passport = [p for p in item.passengers if p['cardType'] != 'PP']
    # if no_passport:
    #     _global_alert_ext_msg.set(f'非护照乘客：{no_passport}')
    #     TBOrderCreateApiCodes.CERTIFICATE_ERROR.raise_error()

    # 非护照不做拦截，改成不生单，手工触碰

    old_verify = await get_verify_session(session_id=item.sessionId)
    if not old_verify:
        _global_alert_ext_msg.set('无验价缓存')
        logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')
        TBOrderCreateApiCodes.NO_RESULT.raise_error()

    if not check_child(passengers=item.passengers, product=old_verify['products'][0]):
        _global_alert_ext_msg.set('儿童余票对比失败')
        logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')
        TBOrderCreateApiCodes.PASSENGER_TYPE_ERROR.raise_error()


async def create_order(item: public_schemas.OrderCreateIn, fare_type: str):
    # fare_sdk = FlightFareSdk(host=settings.FLIGHT_FARE_URL)

    adult = len([p for p in item.passengers if p['ageType'] == 0])
    child = len([p for p in item.passengers if p['ageType'] == 1])
    infant = len([p for p in item.passengers if p['ageType'] == 2])

    fare_key = item.routing['data']
    airline_code = item.routing['fromSegments'][0]['flightNumber'][:2].upper()
    flight_no = item.routing['fromSegments'][0]['flightNumber']
    dep_airport_code = item.routing['fromSegments'][0]['depAirport']
    arr_airport_code = item.routing['fromSegments'][0]['arrAirport']

    # if airline_code not in settings.TB_ALLOW_CREATE_ORDER_AIRLINES:
    #     # 不支持验价占座的航司，先出发异步实时验价
    #     for _ in range(settings.TB_WAIT_TMP_ORDER):
    #         real_time_request = hy_sdks.flight_fare.verify.FlightRealTimeVerifyRequest(
    #             fare_key=fare_key,
    #             dep_airport_code=dep_airport_code,
    #             arr_airport_code=arr_airport_code,
    #             flight_no=flight_no,
    #             adult=adult,
    #             child=child,
    #             infant=infant,
    #             keep_time=settings.TB_TEMP_ORDER_LOCK_TIME * 60,
    #         )
    #         fare_sdk = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    #         real_time_result = await fare_sdk.send_async(request=real_time_request)
    #         logger.debug(real_time_result)
    #         # 验价成功
    #         if real_time_result['code'] == ApiCodes.SUCCESS.value:
    #             return real_time_result
    #         # 继续等待
    #         if real_time_result['code'] == ApiCodes.TASK_RUNNING.value:
    #             await asyncio.sleep(1)
    #             continue
    # 没等到结果走旧的缓存验价
    verify_request = hy_sdks.flight_fare.verify.FlightVerifyRequest(
        fare_key=fare_key,
        dep_airport_code=dep_airport_code,
        arr_airport_code=arr_airport_code,
        flight_no=flight_no,
        adult=adult,
        child=child,
        infant=infant,
    )
    fare_sdk = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    result = await fare_sdk.send_async(request=verify_request)
    logger.debug(result)

    return result


async def parse_create_order(
    result: dict, item: public_schemas.OrderCreateIn, fare_type: str, start_time: datetime = None
):
    # if result['code'] != ApiCodes.SUCCESS.value:
    #     logger.error(result)
    #     tb_code = OrderCreateVerifyToTBCodes.get(result['code'], TBOrderCreateApiCodes.NO_RESULT)
    #     _global_alert_ext_msg.set(f'运价查询（single）接口报错 code:{result["code"]} msg:{result["message"]}')
    #     tb_code.raise_error(ext_msg=result['message'])
    # if not result.get('data'):
    #     _global_alert_ext_msg.set('运价查询（single）接口无数据')
    #     logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')
    #     TBOrderCreateApiCodes.NO_RESULT.raise_error()

    # 锁单逻辑生单拦截
    # 扣除逻辑计划移到运价完成 2025-04-07
    # result['data'] = await remove_locked_product(trip_info=result['data'], total_passenger=len(item.passengers))
    # if not result['data'] or not result['data']['products']:
    #     _global_alert_ext_msg.set('扣除锁定订单后无票')
    #     TBOrderCreateApiCodes.NO_RESULT.raise_error(ext_msg=result['message'])

    old_verify = await get_verify_session(session_id=item.sessionId)
    if not old_verify:
        _global_alert_ext_msg.set(f'验价缓存不存在，session_id:{item.sessionId}')
        logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')
        TBOrderCreateApiCodes.NO_RESULT.raise_error()

    # 判断航班信息
    # if not compare_flight_info(new_verify=result['data'], old_verify=old_verify):
    #     await del_verify_session(session_id=item.sessionId)
    #     _global_alert_ext_msg.set(f'航班对比失败，删除验价缓存，session_id:{item.sessionId}')
    #     logger.debug(f'_global_alert_ext_msg: {_global_alert_ext_msg.get()}')
    #     TBOrderCreateApiCodes.NO_RESULT.raise_error()

    # # 判断价格
    # if not compare_price(new_verify=result['data'], old_verify=old_verify):
    #     await del_verify_session(session_id=item.sessionId)
    #     _global_alert_ext_msg.set(f'价格对比失败，删除验价缓存，session_id:{item.sessionId}')
    #     TBOrderCreateApiCodes.PRICE_CHANGE.raise_error()

    # # 判断乘客
    # product = result['data']['products'][0]
    # if not check_child(passengers=item.passengers, product=product):
    #     await del_verify_session(session_id=item.sessionId)
    #     _global_alert_ext_msg.set(f'儿童余票检查失败，删除验价缓存，session_id:{item.sessionId}')
    #     TBOrderCreateApiCodes.PASSENGER_TYPE_ERROR.raise_error()

    # if not compare_passengers(new_verify=result['data'], old_verify=old_verify):
    #     await del_verify_session(session_id=item.sessionId)
    #     _global_alert_ext_msg.set(f'适用乘客类型对比失败，删除验价缓存，session_id:{item.sessionId}')
    #     TBOrderCreateApiCodes.PASSENGER_TYPE_ERROR.raise_error()

    # # 判断辅营
    # if not compare_baggage(new_verify=result['data'], old_verify=old_verify):
    #     await del_verify_session(session_id=item.sessionId)
    #     _global_alert_ext_msg.set(f'辅营对比失败，删除验价缓存，session_id:{item.sessionId}')
    #     TBOrderCreateApiCodes.PRICE_CHANGE.raise_error()

    # fmt_result = format_verify_result(result)

    # 只要对比通过，就按第一次验价的缓存报价返回，避免自动调价可能的影响
    # mock_result = copy.deepcopy(result)
    mock_result = {}
    mock_result['data'] = copy.deepcopy(old_verify)
    # mock_result['data']['products'][0]['adult']['quantity'] = product['adult']['quantity']
    # if 'child' in product:
    #     mock_result['data']['products'][0]['child']['quantity'] = product['child']['quantity']
    # if 'infant' in product:
    #     mock_result['data']['products'][0]['infant']['quantity'] = product['infant']['quantity']
    fmt_result = format_verify_result(mock_result)

    if 'rules' in fmt_result:
        del fmt_result['rules']
    # 毫秒太长，截掉最后三位
    if settings.TB_MOCK_PNR is True:
        # 生成一个假pnrCode
        fmt_result['pnrCode'] = ''.join(random.sample('ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789', 6))
    fmt_result['sessionId'] = item.sessionId
    fmt_result['orderNo'] = datetime.now().strftime('%Y%m%d%H%M%S%f')[:-3]

    # # 有非护照乘客，不生单（等爬虫完善后再开启）
    # no_passport = [p for p in item.passengers if p['cardType'] != 'PP']

    # # if item.routing['fromSegments'][0]['carrier'] in settings.TB_ALLOW_CREATE_ORDER_AIRLINES and not no_passport:
    # # 是否支持自动预定交给运价模块判断
    # if not no_passport:
    logger.debug(fmt_result)
    fare_key = item.routing['data']
    dep_airport_code = item.routing['fromSegments'][0]['depAirport']
    arr_airport_code = item.routing['fromSegments'][0]['arrAirport']
    flight_no = item.routing['fromSegments'][0]['flightNumber']
    # 真实生单逻辑
    create_order_result = await create_order_real(
        order_no=fmt_result['orderNo'],
        mock_pnr=fmt_result['pnrCode'],
        fare_key=fare_key,
        dep_airport_code=dep_airport_code,
        arr_airport_code=arr_airport_code,
        flight_no=flight_no,
        dep_time=item.routing['fromSegments'][0]['depTime'],
        src_adult_base=old_verify['products'][0]['adult']['src_base'],
        src_adult_tax=old_verify['products'][0]['adult']['src_tax'],
        ota_passengers=item.passengers,
        session_id=item.sessionId,
        passenger_auxes=item.passengerAuxes,
        fare_info=mock_result,
        fare_type=fare_type,
    )
    # 创建成功后等待一段时间，再查询临时订单
    if create_order_result['code'] == ApiCodes.SUCCESS.value:
        await asyncio.sleep(1)
        # 等待临时订单创建完成
        tmp_order_row = None
        for _ in range(settings.TB_CREATE_LOOP_TIMES):
            # 检查是否超时
            if start_time and (datetime.now() - start_time).total_seconds() >= settings.TB_ORDER_CREATE_TIMEOUT:
                logger.warning(
                    f'生单接口超时，已执行时间：{(datetime.now() - start_time).total_seconds()}秒，触发一次实时校验后返回成功'
                )
                # 触发一次实时校验
                try:
                    fare_sdk = get_sub_sdk(module_name='flight_fare', host=settings.FLIGHT_FARE_URL)
                    await fare_sdk.flight_real_time_verify_async(
                        fare_key=item.routing['data'],
                        dep_airport_code=item.routing['fromSegments'][0]['depAirport'],
                        arr_airport_code=item.routing['fromSegments'][0]['arrAirport'],
                        flight_no=item.routing['fromSegments'][0]['flightNumber'],
                        adult=len([p for p in item.passengers if p['ageType'] == 0]),
                        child=len([p for p in item.passengers if p['ageType'] == 1]),
                        infant=len([p for p in item.passengers if p['ageType'] == 2]),
                        keep_time=settings.TB_TEMP_ORDER_LOCK_TIME * 60,
                    )
                except Exception as e:
                    logger.warning(f'超时后触发实时校验失败: {e}')
                break

            tmp_order_row = await get_tmp_order(order_no=fmt_result['orderNo'], mock_pnr=fmt_result['pnrCode'])
            if tmp_order_row['code'] != ApiCodes.TASK_RUNNING.value:
                break
            await asyncio.sleep(1)
        if tmp_order_row and tmp_order_row['code'] in VerifyToTBCodes:
            # 无票退出
            VerifyToTBCodes.get(tmp_order_row['code']).raise_error(ext_msg=tmp_order_row['message'])
    # elif create_order_result['code'] == ApiCodes.FARE_VERIFY_STOP_BOOK.value:
    # else:
    #     try:
    #         adult = len([p for p in item.passengers if p['ageType'] == 0])
    #         child = len([p for p in item.passengers if p['ageType'] == 1])
    #         infant = len([p for p in item.passengers if p['ageType'] == 2])
    #         for _ in range(settings.TB_WAIT_TMP_ORDER):
    #             real_time_request = hy_sdks.flight_fare.verify.FlightRealTimeVerifyRequest(
    #                 fare_key=fare_key,
    #                 dep_airport_code=dep_airport_code,
    #                 arr_airport_code=arr_airport_code,
    #                 flight_no=flight_no,
    #                 adult=adult,
    #                 child=child,
    #                 infant=infant,
    #                 keep_time=settings.TB_TEMP_ORDER_LOCK_TIME * 60,
    #                 mock_pnr=fmt_result['pnrCode'],
    #             )
    #             fare_sdk = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    #             real_time_result = await fare_sdk.send_async(request=real_time_request)
    #             logger.debug(real_time_result)
    #             # 验价成功
    #             if real_time_result['code'] == ApiCodes.SUCCESS.value:
    #                 logger.debug('*' * 20)
    #                 total_passenger = adult + child + infant
    #                 logger.debug(
    #                     f'total_passenger:{total_passenger}, {settings.TB_MULTI_ORDER_TICKET_PROTECT_COEFFICIENT}, real_time_result:{real_time_result["data"]["products"][0]["adult"]["quantity"]}'
    #                 )
    #                 logger.debug('*' * 20)
    #                 if (
    #                     total_passenger > 1
    #                     and total_passenger * settings.TB_MULTI_ORDER_TICKET_PROTECT_COEFFICIENT
    #                     > real_time_result['data']['products'][0]['adult']['quantity']
    #                 ):
    #                     _global_alert_ext_msg.set('多人订单保护，票数不足')
    #                     TBOrderPayVerifyApiCodes.NO_RESULT.raise_error()
    #                 break
    #             # 继续等待
    #             if real_time_result['code'] == ApiCodes.TASK_RUNNING.value:
    #                 await asyncio.sleep(1)
    #                 continue
    #     except Exception as e:
    #         logger.error(e)

    # fmt_result['pnrCode']
    # 在验价格式基础上再判断
    return fmt_result


async def order_pay_verify(item: public_schemas.OrderPayVerifyIn, fare_type: str):
    tmp_order_row = await TBTempOrder.get_by_async(TBTempOrder.order_no == item.orderNo)

    if not tmp_order_row:
        _global_alert_ext_msg.set(f'查询临时订单失败，order_no:{item.orderNo}')
        TBOrderPayVerifyApiCodes.PAY_TIMEOUT.raise_error()

    if tmp_order_row['canceled'] == 1:
        _global_alert_ext_msg.set(f'订单已取消，order_no:{item.orderNo}')
        TBOrderPayVerifyApiCodes.PAY_TIMEOUT.raise_error()

    old_verify = await get_verify_session(session_id=item.sessionId)
    if not old_verify:
        _global_alert_ext_msg.set(f'无验价缓存，order_no:{item.orderNo} session_id:{item.sessionId}')
        TBOrderPayVerifyApiCodes.NO_RESULT.raise_error()

    adult = tmp_order_row['adult']
    child = tmp_order_row['child']
    infant = tmp_order_row['infant']

    fare_key = item.routing['data']
    airline_code = item.routing['fromSegments'][0]['flightNumber'][:2].upper()
    flight_no = item.routing['fromSegments'][0]['flightNumber']
    dep_airport_code = item.routing['fromSegments'][0]['depAirport']
    arr_airport_code = item.routing['fromSegments'][0]['arrAirport']

    # if airline_code not in settings.TB_ALLOW_CREATE_ORDER_AIRLINES:
    real_time_result = None
    # 不支持验价占座的航司，先出发异步实时验价
    for _ in range(settings.TB_PAY_VERIFY_LOOP_TIMES):
        real_time_request = hy_sdks.flight_fare.verify.FlightRealTimeVerifyRequest(
            fare_key=fare_key,
            dep_airport_code=dep_airport_code,
            arr_airport_code=arr_airport_code,
            flight_no=flight_no,
            adult=adult,
            child=child,
            infant=infant,
            keep_time=settings.TB_TEMP_ORDER_LOCK_TIME * 60,
            mock_pnr=item.pnrCode,
        )
        fare_sdk = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
        real_time_result = await fare_sdk.send_async(request=real_time_request)
        logger.debug(real_time_result)
        # 验价成功
        if real_time_result['code'] == ApiCodes.SUCCESS.value:
            if 'data' in real_time_result:
                total_passenger = adult + child + infant
                logger.debug(
                    f'total_passenger:{total_passenger}, {total_passenger} * {settings.TB_MULTI_ORDER_TICKET_PROTECT_COEFFICIENT} = {ceil(total_passenger * settings.TB_MULTI_ORDER_TICKET_PROTECT_COEFFICIENT)}, real_time_result:{real_time_result["data"]["products"][0]["adult"]["quantity"]}'
                )
                if (
                    total_passenger > 1
                    and ceil(total_passenger * settings.TB_MULTI_ORDER_TICKET_PROTECT_COEFFICIENT)
                    > real_time_result['data']['products'][0]['adult']['quantity']
                ):
                    _global_alert_ext_msg.set('多人订单保护，票数不足')
                    TBOrderPayVerifyApiCodes.NO_RESULT.raise_error()
            return real_time_result
        # 继续等待
        if real_time_result['code'] == ApiCodes.TASK_RUNNING.value:
            await asyncio.sleep(1)
            continue
        else:
            break

    if not real_time_result:
        ApiCodes.FARE_VERIFY_PAY_TIMEOUT.raise_error()

    return real_time_result


async def parse_pay_verify_result(result: dict, item: public_schemas.OrderPayVerifyIn):
    if result['code'] != ApiCodes.SUCCESS.value:
        logger.error(result)
        tb_code = OrderPayVerifyToTBCodes.get(result['code'], TBPVerifyApiCodes.EXCEPT_ERROR)
        _global_alert_ext_msg.set(f'运价查询（single）接口报错 code:{result["code"]} msg:{result["message"]}')
        tb_code.raise_error(ext_msg=result['message'])
    if not result.get('data'):
        _global_alert_ext_msg.set('运价查询（single）接口无数据')
        TBOrderPayVerifyApiCodes.NO_RESULT.raise_error()

    # 解锁单逻辑生单拦截
    old_verify = await get_verify_session(session_id=item.sessionId)
    if not old_verify:
        _global_alert_ext_msg.set('无验价缓存')
        TBOrderCreateApiCodes.NO_RESULT.raise_error()

    # 判断航班信息
    if not compare_flight_info(new_verify=result['data'], old_verify=old_verify):
        await del_verify_session(session_id=item.sessionId)
        _global_alert_ext_msg.set(f'航班信息不一致，删除验价缓存，session_id:{item.sessionId}')
        TBOrderCreateApiCodes.NO_RESULT.raise_error()

    # 判断价格
    if not compare_price(new_verify=result['data'], old_verify=old_verify):
        await del_verify_session(session_id=item.sessionId)
        _global_alert_ext_msg.set(f'价格不一致，删除验价缓存，session_id:{item.sessionId}')
        TBOrderCreateApiCodes.PRICE_CHANGE.raise_error()

    # 只在有儿童和婴儿时才校验其他乘客类型节点
    tmp_order_row = await TBTempOrder.get_by_async(TBTempOrder.order_no == item.orderNo)
    if tmp_order_row.get('child', 0) > 0 or tmp_order_row.get('infant', 0) > 0:
        # 判断乘客
        if not compare_passengers(new_verify=result['data'], old_verify=old_verify):
            await del_verify_session(session_id=item.sessionId)
            _global_alert_ext_msg.set(f'适用乘客类型不一致，删除验价缓存，session_id:{item.sessionId}')
            TBOrderCreateApiCodes.PASSENGER_TYPE_ERROR.raise_error()

    # 判断辅营
    if not compare_baggage(new_verify=result['data'], old_verify=old_verify):
        await del_verify_session(session_id=item.sessionId)
        _global_alert_ext_msg.set(f'辅营不一致，删除验价缓存，session_id:{item.sessionId}')
        TBOrderCreateApiCodes.PRICE_CHANGE.raise_error()

    # fmt_result = format_verify_result(result)

    # 只要对比通过，就按第一次验价的缓存报价返回，避免自动调价可能的影响
    mock_result = copy.deepcopy(result)
    mock_result['data'] = old_verify
    mock_result['data']['products'][0]['adult']['quantity'] = result['data']['products'][0]['adult']['quantity']
    if 'child' in result['data']['products'][0]:
        mock_result['data']['products'][0]['child']['quantity'] = result['data']['products'][0]['child']['quantity']
    if 'infant' in result['data']['products'][0]:
        mock_result['data']['products'][0]['infant']['quantity'] = result['data']['products'][0]['infant']['quantity']
    fmt_result = format_verify_result(mock_result)

    if 'rules' in fmt_result:
        del fmt_result['rules']
    # 毫秒太长，截掉最后三位
    fmt_result['sessionId'] = item.sessionId
    fmt_result['orderNo'] = item.orderNo
    fmt_result['pnrCode'] = item.pnrCode
    # fmt_result['pnrCode']
    # 在验价格式基础上再判断
    return fmt_result


async def set_verify_session(result: dict):
    session_id = 'S' + str(int(time.time() * 1000))
    async with redis_pool as redis:
        await redis.set(f'tb_verify_{session_id}', orjson.dumps(result).decode('utf-8'), ex=60 * 60 * 24)
    return session_id


async def get_verify_session(session_id: str):
    async with redis_pool as redis:
        verify_result = await redis.get(f'tb_verify_{session_id}')
        if verify_result:
            return orjson.loads(verify_result)
    return None


async def del_verify_session(session_id: str):
    async with redis_pool as redis:
        await redis.delete(f'tb_verify_{session_id}')


def compare_flight_info(new_verify, old_verify):
    new_flight = new_verify['flight_info']
    old_flight = old_verify['flight_info']
    # 判断航班信息
    if new_flight['flight_no'] != old_flight['flight_no']:
        logger.warning(f'航班号不一致，new:{new_flight["flight_no"]}, old:{old_flight["flight_no"]}')
        return False
    if new_flight['dep_date'] != old_flight['dep_date']:
        logger.warning(f'航班日期不一致，new:{new_flight["dep_date"]}, old:{old_flight["dep_date"]}')
        return False
    if new_flight['dep_airport_code'] != old_flight['dep_airport_code']:
        logger.warning(f'起飞机场不一致，new:{new_flight["dep_airport_code"]}, old:{old_flight["dep_airport_code"]}')
        return False
    if new_flight['arr_airport_code'] != old_flight['arr_airport_code']:
        logger.warning(f'到达机场不一致，new:{new_flight["arr_airport_code"]}, old:{old_flight["arr_airport_code"]}')
        return False
    logger.info('航班信息检查通过')
    return True


def check_child(passengers: list, product: dict):
    child = len([p for p in passengers if p['ageType'] == 1])
    infant = len([p for p in passengers if p['ageType'] == 2])

    if child > 0 and (not product.get('child') or product.get('child', {}).get('quantity') == 0):
        logger.warning(f'航班儿童票不足, childs:{len(child)}, product:{product}')
        return False

    if infant > 0 and (not product.get('infant') or product.get('infant', {}).get('quantity') == 0):
        logger.warning(f'航班婴儿票不足, infants:{len(infant)}, product:{product}')
        return False
    logger.info('余票检查通过')
    return True


def compare_price(new_verify, old_verify):
    new_product = new_verify['products'][0]
    old_product = old_verify['products'][0]
    logger.debug(f'new_product:{new_product}, old_product:{old_product}')
    if new_product['cabin_class'] != old_product['cabin_class']:
        logger.warning(
            f'舱位等级不相同, new_cabin_class:{new_product["cabin_class"]}, old_cabin_class:{old_product["cabin_class"]}'
        )
        return False
    # if new_product['adult']['base'] > old_product['adult']['base']:
    #     logger.warning(
    #         f'成人节点报价不相同, new_adult_base:{new_product["adult"]["base"]}, old_adult_base:{old_product["adult"]["base"]}'
    #     )
    #     return False
    # if new_product['adult']['tax'] > old_product['adult']['tax']:
    #     logger.warning(
    #         f'成人节点税费不相同, new_adult_tax:{new_product["adult"]["tax"]}, old_adult_tax:{old_product["adult"]["tax"]}'
    #     )
    #     return False

    # 对成本价格进程统一处理
    old_product['adult']['src_base'] = round(float(old_product['adult']['src_base']), 2)
    old_product['adult']['src_tax'] = round(float(old_product['adult']['src_tax']), 2)
    new_product['adult']['src_base'] = round(float(new_product['adult']['src_base']), 2)
    new_product['adult']['src_tax'] = round(float(new_product['adult']['src_tax']), 2)

    if new_product['adult']['src_base'] > old_product['adult']['src_base']:
        logger.warning(
            f'成人节点原币报价不相同, new_adult_src_base:{new_product["adult"]["src_base"]}, old_adult_src_base:{old_product["adult"]["src_base"]}'
        )
        return False
    if new_product['adult']['src_tax'] > old_product['adult']['src_tax']:
        logger.warning(
            f'成人节点原币税费不相同, new_adult_src_tax:{new_product["adult"]["src_tax"]}, old_adult_src_tax:{old_product["adult"]["src_tax"]}'
        )
        return False
    if new_product['adult']['src_currency'] != old_product['adult']['src_currency']:
        logger.warning(
            f'成人节点原币不相同, new_adult_src_currency:{new_product["adult"]["src_currency"]}, old_adult_src_currency:{old_product["adult"]["src_currency"]}'
        )
        return False
    logger.info('价格校验通过')
    return True


def compare_passengers(new_verify, old_verify):
    new_product = new_verify['products'][0]
    old_product = old_verify['products'][0]

    if ('child' in new_product and 'child' not in old_product) or (
        'child' not in new_product and 'child' in old_product
    ):
        logger.warning(f'儿童节点不相同, new_product:{new_product}, old_product:{old_product}')
        return False
    if (
        new_product.get('child')
        and old_product.get('child')
        and (
            # new_product['child']['base'] > old_product['child']['base']
            # or new_product['child']['tax'] > old_product['child']['tax']
            # or
            new_product['child']['src_base'] > old_product['child']['src_base']
            or new_product['child']['src_tax'] > old_product['child']['src_tax']
            or new_product['child']['src_currency'] != old_product['child']['src_currency']
        )
    ):
        logger.warning(f'儿童节点报价不相同, new_product:{new_product}, old_product:{old_product}')
        return False

    if ('infant' in new_product and 'infant' not in old_product) or (
        'infant' not in new_product and 'infant' in old_product
    ):
        logger.warning(f'婴儿节点不相同, new_product:{new_product}, old_product:{old_product}')
        return False

    if (
        new_product.get('infant')
        and old_product.get('infant')
        and (
            # new_product['infant']['base'] > old_product['infant']['base']
            # or new_product['infant']['tax'] > old_product['infant']['tax']
            # or
            new_product['infant']['src_base'] > old_product['infant']['src_base']
            or new_product['infant']['src_tax'] > old_product['infant']['src_tax']
            or new_product['infant']['src_currency'] != old_product['infant']['src_currency']
        )
    ):
        logger.warning(f'婴儿节点报价不相同, new_product:{new_product}, old_product:{old_product}')
        return False
    logger.info('可售乘客类型校验通过')
    return True


def compare_baggage(new_verify, old_verify):
    new_product = new_verify['products'][0]
    old_product = old_verify['products'][0]
    # 判断辅营信息
    new_baggage = new_product.get('includes', {}).get('baggage', {}).get('checked_baggage', None)
    old_baggage = old_product.get('includes', {}).get('baggage', {}).get('checked_baggage', None)
    new_baggage_str = (
        ','.join(
            [f'{x["weight"]},{x["count"]},{x["all_weight"]}' for x in sorted(new_baggage, key=lambda x: x['weight'])]
        )
        if new_baggage
        else ''
    )
    old_baggage_str = (
        ','.join(
            [f'{x["weight"]},{x["count"]},{x["all_weight"]}' for x in sorted(old_baggage, key=lambda x: x['weight'])]
        )
        if old_baggage
        else ''
    )
    if new_baggage_str != old_baggage_str:
        logger.warning(f'辅营信息不相同, new_baggage:{new_baggage}, old_baggage:{old_baggage}')
        return False

    new_child_baggage = new_product.get('child_includes', {}).get('baggage', {}).get('checked_baggage', None)
    old_child_baggage = old_product.get('child_includes', {}).get('baggage', {}).get('checked_baggage', None)
    new_child_baggage_str = (
        ','.join(
            [
                f'{x["weight"]},{x["count"]},{x["all_weight"]}'
                for x in sorted(new_child_baggage, key=lambda x: x['weight'])
            ]
        )
        if new_child_baggage
        else ''
    )
    old_child_baggage_str = (
        ','.join(
            [
                f'{x["weight"]},{x["count"]},{x["all_weight"]}'
                for x in sorted(old_child_baggage, key=lambda x: x['weight'])
            ]
        )
        if old_child_baggage
        else ''
    )
    if new_child_baggage_str != old_child_baggage_str:
        logger.warning(
            f'儿童辅营信息不相同, new_child_baggage:{new_child_baggage}, old_child_baggage:{old_child_baggage}'
        )
        return False

    new_infant_baggage = new_product.get('infant_includes', {}).get('baggage', {}).get('checked_baggage', None)
    old_infant_baggage = old_product.get('infant_includes', {}).get('baggage', {}).get('checked_baggage', None)
    new_infant_baggage_str = (
        ','.join(
            [
                f'{x["weight"]},{x["count"]},{x["all_weight"]}'
                for x in sorted(new_infant_baggage, key=lambda x: x['weight'])
            ]
        )
        if new_infant_baggage
        else ''
    )
    old_infant_baggage_str = (
        ','.join(
            [
                f'{x["weight"]},{x["count"]},{x["all_weight"]}'
                for x in sorted(old_infant_baggage, key=lambda x: x['weight'])
            ]
        )
        if old_infant_baggage
        else ''
    )
    if new_infant_baggage_str != old_infant_baggage_str:
        logger.warning(
            f'婴儿辅营信息不相同, new_infant_baggage:{new_infant_baggage}, old_infant_baggage:{old_infant_baggage}'
        )
        return False

    logger.info('辅营信息校验通过')
    return True


def convert_tb_passenger_aux(aux: dict):
    dep_time = datetime.strptime(aux['segment']['depTime'], '%Y%m%d%H%M')
    tmp = {
        'name': aux['name'],
        'flight_no': aux['segment']['flightNumber'],
        'dep_airport_code': aux['segment']['depAirport'],
        'arr_airport_code': aux['segment']['arrAirport'],
        'dep_date': dep_time.strftime('%Y-%m-%d'),
        'dep_time': dep_time.strftime('%H:%M'),
        'outer_id': aux['productItem']['outerId'],
        'aux_type': AuxType.BAGGAGE.value,
        'weight': aux['productItem']['baggage']['weight'],
        'size': 0,
        'count': aux['productItem']['baggage']['pc'],
        'price': aux['productItem']['onlinePrice'],
        'desc': aux['productItem']['productName'],
    }
    return tmp


def convert_tb_passenger_aux_from_fare_info(
    flight_no: str, dep_airport_code: str, arr_airport_code: str, dep_time: str, passenger: dict, baggage: dict
):
    dep_time = datetime.strptime(dep_time, '%Y%m%d%H%M')
    tmp = {
        'name': passenger['name'],
        'flight_no': flight_no,
        'dep_airport_code': dep_airport_code,
        'arr_airport_code': arr_airport_code,
        'dep_date': dep_time.strftime('%Y-%m-%d'),
        'dep_time': dep_time.strftime('%H:%M'),
        'outer_id': '',
        'aux_type': AuxType.BAGGAGE.value,
        'weight': baggage['weight'],
        'size': 0,
        'count': baggage['count'],
        'price': 0,
        'desc': orjson.dumps(baggage).decode('utf-8'),
    }
    return tmp


async def create_order_real(
    order_no: str,
    mock_pnr: str,
    fare_key: str,
    dep_airport_code: str,
    arr_airport_code: str,
    flight_no: str,
    dep_time: str,
    src_adult_base: float,
    src_adult_tax: float,
    ota_passengers: list,
    session_id: str,
    fare_info: dict,
    fare_type: str,
    passenger_auxes: list = None,
):
    passengers = []
    adult = 0
    child = 0
    infant = 0
    for passenger in ota_passengers:
        if passenger['ageType'] == 0:
            adult += 1
        elif passenger['ageType'] == 1:
            child += 1
        elif passenger['ageType'] == 2:
            infant += 1
        passengers.append(convert_tb_passenger(passenger))

    fmt_passenger_auxes = []
    if passenger_auxes:
        for aux in passenger_auxes:
            if aux['productItem']['productType'] == 4:
                fmt_passenger_auxes.append(convert_tb_passenger_aux(aux))
    logger.debug(f'fare_info:{fare_info}')

    if fare_info['data'].get('products', [{}])[0].get('includes', {}).get('baggage', {}).get('checked_baggage', []):
        logger.debug(f'fare_info:{fare_info}')
        for passenger in passengers:
            fmt_passenger_auxes.append(
                convert_tb_passenger_aux_from_fare_info(
                    flight_no=flight_no,
                    dep_airport_code=dep_airport_code,
                    arr_airport_code=arr_airport_code,
                    dep_time=dep_time,
                    passenger=passenger,
                    baggage=fare_info['data']['products'][0]['includes']['baggage']['checked_baggage'][0],
                )
            )
    else:
        logger.debug(f'fare_info:{fare_info}')

    req = hy_sdks.flight_fare.verify.FlightVerifyOrderCreateRequest(
        order_no=order_no,
        mock_pnr=mock_pnr,
        fare_key=fare_key,
        dep_airport_code=dep_airport_code,
        arr_airport_code=arr_airport_code,
        flight_no=flight_no,
        dep_time=dep_time,
        src_adult_base=src_adult_base,
        src_adult_tax=src_adult_tax,
        adult=adult,
        child=child,
        infant=infant,
        passengers=passengers,
        session_id=session_id,
        passenger_auxes=fmt_passenger_auxes,
        fare_type=fare_type,
    )
    logger.debug(f'req:{req}')
    # raise Exception('test')
    sdk_client = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    result = await sdk_client.send_async(request=req)
    logger.debug(result)
    return result


def convert_tb_passenger(passenger: dict):

    # ***** 原始数据
    # {
    #     "ageType": 0,
    #     "birthday": "19970517",
    #     "cardExpired": "20250911",
    #     "cardIssuePlace": "CN",
    #     "cardNum": "HSJUSHJDKKD",
    #     "cardType": "PP",
    #     "gender": "M",
    #     "name": "DUAN/BEN",
    #     "nationality": "CN"
    # }
    tmp = {
        "passenger_type": PassengerType(passenger['ageType']).name.lower(),
        "name": passenger['name'],
        "last_name": passenger['name'].split('/')[0],
        "first_name": ' '.join(passenger['name'].split('/')[1:]),
        "sex": SexType(passenger['gender']).name.lower(),
        "country": passenger['nationality'],
        "card_no": passenger.get('cardNum', ''),
        "card_valid_date": datetime.strptime(passenger.get('cardExpired', ''), '%Y%m%d').strftime('%Y-%m-%d'),
        "card_country": passenger.get('cardIssuePlace', passenger.get('nationality', '')),
        "birthday": datetime.strptime(passenger.get('birthday', ''), '%Y%m%d').strftime('%Y-%m-%d'),
    }
    return tmp


async def order_pay_verify_real(item: public_schemas.OrderPayVerifyIn, fare_type: str):
    tmp_order_row = await TBTempOrder.get_by_async(TBTempOrder.order_no == item.orderNo)

    if not tmp_order_row:
        _global_alert_ext_msg.set(f'查询临时订单失败，order_no:{item.orderNo}')
        # TBOrderPayVerifyApiCodes.PAY_TIMEOUT.raise_error()
        return None

    if tmp_order_row['canceled'] == 1:
        _global_alert_ext_msg.set(f'订单已取消，order_no:{item.orderNo}')
        TBOrderPayVerifyApiCodes.PAY_TIMEOUT.raise_error()

    old_verify = await get_verify_session(session_id=item.sessionId)
    if not old_verify:
        _global_alert_ext_msg.set(f'无验价缓存，order_no:{item.orderNo} session_id:{item.sessionId}')
        TBOrderPayVerifyApiCodes.NO_RESULT.raise_error()

    tmp_response = orjson.loads(tmp_order_row['response'])
    # req = hy_sdks.flight_fare.verify.FlightVerifyOrderGetRequest(
    #     order_no=item.orderNo, mock_pnr=tmp_response['pnrCode']
    # )
    # sdk_client = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    # result = await sdk_client.send_async(request=req)

    for _ in range(settings.TB_CREATE_LOOP_TIMES):
        result = await get_tmp_order(order_no=item.orderNo, mock_pnr=tmp_response['pnrCode'])
        if result['code'] != ApiCodes.TASK_RUNNING.value:
            break
        await asyncio.sleep(1)

    if result['code'] == ApiCodes.SUCCESS.value and result['data']['real_pnr']:
        # 如果有pnr，直接返回验证成功
        return {"code": ApiCodes.SUCCESS.value, "data": old_verify, "message": result['message']}
    elif result['code'] != ApiCodes.SUCCESS.value and result['code'] in OrderPayVerifyToTBCodes:
        # 如果失败，且错误码在映射表中，直接返回验价失败
        tb_code = OrderPayVerifyToTBCodes.get(result['code'], TBPVerifyApiCodes.EXCEPT_ERROR)
        tb_code.raise_error(ext_msg=result['message'])
    # 返回None则继续走爬虫查询验价
    return None


async def get_tmp_order(order_no: str, mock_pnr: str):
    req = hy_sdks.flight_fare.verify.FlightVerifyOrderGetRequest(order_no=order_no, mock_pnr=mock_pnr)
    sdk_client = hy_sdks.base.SdkClient(host=settings.FLIGHT_FARE_URL)
    result = await sdk_client.send_async(request=req)
    return result
