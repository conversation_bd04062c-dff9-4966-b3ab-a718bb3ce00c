# VZ境内线A舱THB规则实现文档

## 需求概述

VZ境内线A舱固定使用THB进行查询预定：
- **境内线定义**：出发、到达都是泰国境内机场
- **适用条件**：VZ航空公司 + A舱位 + 泰国境内线
- **实现方式**：在调用book前，查询航班时判断是否符合条件，符合则强制用THB重新查询

## 实现方案

### 实现位置
- **不修改** `run_search` 函数
- **只修改** `agent_service.py` 中调用了 book 的地方
- **判断时机**：在获取完预定目标航班之后，因为要判断航班舱位
- **处理逻辑**：舱位符合的话重新执行查询逻辑

### 重要修正
1. **舱位判断逻辑**：从 `trips[0].segments[0].cabin.code` 或 `trips[0].cabin_codes[0]` 获取舱位代码，并取第一个字符进行判断
2. **重新查询前置操作**：必须先调用 `sv.back_index()` 回到首页，然后才能进行THB查询
3. **严格舱位匹配**：只有明确是A舱（舱位代码第一个字符为'A'）才应用THB规则，空舱位或其他舱位不应用

### 1. 配置文件
创建 `configs/vz_domestic_thb_rules.toml` 配置文件：

```toml
[vz_domestic_thb_rules]
# 是否启用VZ境内线THB规则
enabled = true

# 适用的航空公司代码
airline_codes = ["VZ"]

# 适用的舱位等级
cabin_classes = ["A"]

# 强制使用的币种
force_currency = "THB"

# 泰国机场代码列表（用于动态判断境内线）
thailand_airports = [
    # 国际机场
    "BKK", "DMK", "BTZ", "CNX", "CEI", "HDY", "KKC", "USM", "KBV", "NST", "HKT", "UTP", "URT", "UTH",
    # 国内机场
    "BFV", "CJM", "HHQ", "LPT", "LOE", "HGN", "MAQ", "KOP", "NAK", "NNT", "NAW", "PHY", "PHS", "PRH",
    "UNN", "ROI", "SNO", "THS", "TST", "TDX", "UBP",
    # 军用/私人/特殊用途机场（仍属泰国境内）
    "QHI", "KDT", "KKM", "TKH", "PYY", "PAN", "PHZ", "SGZ", "PXR", "TKT", "UTR"
]

# 泰国境内线路列表（从提供的数据中筛选出的境内线，共21条）
domestic_routes = [
    "BKK-CEI", "BKK-CNX", "BKK-HDY", "BKK-HKT", "BKK-KBV", "BKK-KKC", 
    "BKK-UBP", "BKK-URT", "BKK-UTH", "CEI-BKK", "CEI-HKT", "CNX-BKK", 
    "CNX-HKT", "HDY-BKK", "HKT-BKK", "HKT-CNX", "KBV-BKK", "KKC-BKK", 
    "UBP-BKK", "URT-BKK", "UTH-BKK"
]
```

### 2. 核心函数

在 `app/services/helper_service.py` 中实现：

#### `is_thailand_domestic_route(dep_airport, arr_airport)`
- 判断是否为泰国境内线
- 从配置文件读取泰国机场列表
- 返回布尔值

#### `should_use_thb_for_vz_domestic(task_data)`
- 检查是否符合VZ境内线A舱条件
- 验证航空公司代码、舱位等级、境内线
- 返回布尔值

#### `apply_vz_domestic_thb_rule(task_data)`
- 应用VZ境内线THB规则
- 创建修改后的任务数据副本
- 强制设置币种为THB
- 返回修改后的任务数据

### 3. 集成到预定服务

在 `app/services/agent_service.py` 的 `run_book` 和 `run_verify_book` 函数中，在获取完预定目标航班后添加VZ境内线A舱THB规则判断：

```python
# 检查VZ境内线A舱THB规则
from app.services.helper_service import should_use_thb_for_vz_domestic

# 构建任务数据用于规则判断
# 获取舱位代码的第一个字符
cabin_code = ''
if flight['trips'][0].get('cabin_codes') and flight['trips'][0]['cabin_codes']:
    cabin_code = flight['trips'][0]['cabin_codes'][0][0]  # 取第一个舱位代码的第一个字符
elif (flight['trips'][0].get('segments') and
      flight['trips'][0]['segments'] and
      flight['trips'][0]['segments'][0].get('cabin', {}).get('code')):
    cabin_code = flight['trips'][0]['segments'][0]['cabin']['code'][0]  # 取第一个字符

task_data_for_rule = {
    'airline_code': params['airline_code'],
    'dep_airport_code': params['dep_airport_code'],
    'arr_airport_code': params['arr_airport_code'],
    'cabin_class': cabin_code,  # 从航班信息获取舱位代码的第一个字符
    'currency_code': params['currency_code']
}

# 如果符合VZ境内线A舱条件且当前不是THB，则用THB重新查询
if should_use_thb_for_vz_domestic(task_data_for_rule) and params['currency_code'] != 'THB':
    logger.info(f'VZ境内线A舱检测: 符合条件，使用THB重新查询')

    # 重新查询前先回到首页
    sv.back_index()

    # 用THB重新查询
    search_rs_thb = sv.search(
        dep=params['dep_airport_code'],
        arr=params['arr_airport_code'],
        date=params['dep_date'],
        adult=params['adult'],
        child=params['child'],
        infant=params['infant'],
        currency_code='THB',
    )

    if search_rs_thb and search_rs_thb['results']:
        # 查找相同航班号的航班
        flight_thb = [f for f in search_rs_thb['results'] if f['trips'][0]['flight_nos'][0] == params['flight_no']]
        if flight_thb:
            flight = flight_thb[0]
            params['currency_code'] = 'THB'  # 更新币种参数
            logger.info(f'VZ境内线A舱: 成功切换到THB币种，使用新航班信息')
        else:
            logger.warning(f'VZ境内线A舱: THB查询中未找到目标航班，继续使用原币种')
    else:
        logger.warning(f'VZ境内线A舱: THB查询失败，继续使用原币种')
```

### 4. 配置管理器更新

在 `app/config.py` 中添加新配置文件：

```python
new_settings = ConfigManager(settings_files=[
    os.path.join(settings.ROOT_PATH, 'configs', 'agent_account_rules.toml'),
    os.path.join(settings.ROOT_PATH, 'configs', 'vz_domestic_thb_rules.toml')
])
```

## 航线数据分析

从提供的42条航线数据中识别出21条泰国境内线：

### 境内线列表
- BKK-CEI (曼谷素万那普-清莱)
- BKK-CNX (曼谷素万那普-清迈)
- BKK-HDY (曼谷素万那普-合艾)
- BKK-HKT (曼谷素万那普-普吉)
- BKK-KBV (曼谷素万那普-甲米)
- BKK-KKC (曼谷素万那普-孔敬)
- BKK-UBP (曼谷素万那普-乌汶)
- BKK-URT (曼谷素万那普-素叻他尼)
- BKK-UTH (曼谷素万那普-乌隆他尼)
- CEI-BKK (清莱-曼谷素万那普)
- CEI-HKT (清莱-普吉)
- CNX-BKK (清迈-曼谷素万那普)
- CNX-HKT (清迈-普吉)
- HDY-BKK (合艾-曼谷素万那普)
- HKT-BKK (普吉-曼谷素万那普)
- HKT-CNX (普吉-清迈)
- KBV-BKK (甲米-曼谷素万那普)
- KKC-BKK (孔敬-曼谷素万那普)
- UBP-BKK (乌汶-曼谷素万那普)
- URT-BKK (素叻他尼-曼谷素万那普)
- UTH-BKK (乌隆他尼-曼谷素万那普)

### 国际线（21条）
包括泰国与印度、越南、日本、中国、台湾、柬埔寨等地的航线。

## 测试验证

创建了完整的测试套件验证功能：

1. **泰国境内线判断测试** - 验证机场代码识别
2. **VZ规则条件测试** - 验证航空公司、舱位、境内线条件
3. **agent_service集成测试** - 验证在预定流程中的THB规则应用
4. **不同场景测试** - 验证各种币种和舱位组合的处理逻辑
5. **配置文件测试** - 验证配置加载和境内线数据

测试脚本：
- `scripts/test_vz_thb_rules.py` - 基础规则测试
- `scripts/test_agent_vz_thb_rules.py` - agent_service集成测试

所有测试均通过，功能正常工作。

## 使用说明

1. **启用/禁用规则**：修改配置文件中的 `enabled` 参数
2. **添加新机场**：在 `thailand_airports` 列表中添加机场代码
3. **修改适用条件**：调整 `airline_codes` 和 `cabin_classes` 列表
4. **更改强制币种**：修改 `force_currency` 参数

## 日志记录

系统会记录以下关键信息：
- VZ境内线A舱检测结果
- 币种强制转换操作
- 配置加载状态
- 错误和异常信息

## 注意事项

1. 配置文件使用TOML格式，语法严格
2. 机场代码不区分大小写
3. 原始任务数据不会被修改，返回副本
4. 规则仅在VJ搜索服务中生效
5. 支持动态配置更新，无需重启服务
