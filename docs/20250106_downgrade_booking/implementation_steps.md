# 降舱出票功能实施步骤（插件化方案）

## 阶段一：数据库设计和基础模型 (1-2天)

### 1.1 数据库变更
1. **新增任务状态常量**
   - 修改 `app/consts/types.py`
   - 添加降舱出票任务状态枚举
   - 定义任务状态转换规则

2. **创建数据库迁移文件**
   - 使用 alembic 创建迁移文件
   - 新增 `downgrade_booking_tasks` 表
   - 新增 `downgrade_booking_scan_records` 表
   - 修改 `flight_orders` 表仅添加一个锁定字段

3. **执行数据库迁移**
   - 在开发环境执行迁移
   - 验证表结构正确性
   - 创建必要的索引和外键约束

### 1.2 基础数据模型
1. **创建降舱任务模型**
   - 新建 `app/models/downgrade_booking_task.py`
   - 实现任务生命周期管理方法
   - 添加任务状态转换逻辑

2. **创建扫描记录模型**
   - 新建 `app/models/downgrade_booking_scan_record.py`
   - 实现扫描记录保存和查询方法
   - 添加JSON字段处理和关联查询

3. **最小化修改订单模型**
   - 修改 `app/models/flight_order.py`
   - 仅添加 `downgrade_booking_enabled` 字段
   - 保持现有模型结构不变

### 1.3 订单锁定机制
1. **创建订单锁定服务**
   - 新建 `app/services/order_lock_services.py`
   - 实现订单锁定和解锁逻辑
   - 添加锁定状态检查方法

2. **创建权限检查装饰器**
   - 修改 `app/decorators.py`
   - 添加降舱锁定检查装饰器
   - 实现操作权限验证

## 阶段二：核心业务逻辑 (3-4天)

### 2.1 降舱出票插件服务
1. **创建降舱任务管理器**
   - 新建 `app/services/downgrade_booking_services.py`
   - 实现任务创建、更新、取消逻辑
   - 实现任务状态管理和转换
   - 集成订单锁定机制

2. **实现舱位扫描服务**
   - 实现舱位扫描和比较逻辑
   - 集成现有运价查询API
   - 实现价格差异计算
   - 添加扫描记录保存

3. **实现出票执行逻辑**
   - 集成现有出票流程
   - 实现降舱出票触发
   - 添加出票结果处理
   - 实现订单解锁机制

### 2.2 任务调度扩展
1. **扩展任务服务**
   - 修改 `app/services/task_services.py`
   - 添加降舱扫描任务构建
   - 实现任务推送和调度逻辑
   - 添加任务清理机制

2. **实现插件式调度器**
   - 创建独立的降舱出票调度器
   - 实现定期扫描机制
   - 添加并发控制和资源管理
   - 实现任务超时处理

3. **集成现有任务系统**
   - 与现有Celery任务系统集成
   - 实现任务状态同步
   - 添加任务监控和告警
   - 保持与现有系统的兼容性

### 2.3 异常处理和恢复
1. **实现异常处理机制**
   - 添加网络异常处理
   - 实现出票失败恢复
   - 添加任务超时处理
   - 实现订单锁定异常恢复

2. **实现数据一致性保证**
   - 使用数据库事务确保一致性
   - 实现任务状态回滚机制
   - 添加数据校验和修复
   - 实现锁定状态同步

## 阶段三：API接口开发 (2-3天)

### 3.1 降舱出票管理接口
1. **创建插件管理视图**
   - 新建 `app/views/downgrade_booking_views.py`
   - 实现启用降舱出票接口（创建任务+锁定订单）
   - 实现取消降舱出票接口（取消任务+解锁订单）
   - 实现任务配置更新接口

2. **实现任务查询接口**
   - 降舱出票任务列表查询
   - 任务详情和状态查询
   - 扫描记录历史查询
   - 任务统计和监控接口

3. **实现权限控制接口**
   - 添加订单锁定状态检查
   - 实现操作权限验证
   - 添加批量操作支持

### 3.2 数据模式定义
1. **创建插件专用模式**
   - 新建 `app/views/schemas/downgrade_booking_schemas.py`
   - 定义任务创建和更新请求模式
   - 定义任务查询响应模式
   - 定义扫描记录数据模式

2. **最小化扩展现有模式**
   - 修改 `app/views/schemas/order_schemas.py`
   - 仅添加降舱锁定状态字段
   - 保持现有接口结构不变

### 3.3 现有接口权限扩展
1. **订单操作接口保护**
   - 修改 `app/views/order_views.py`
   - 在关键操作接口添加锁定检查装饰器
   - 添加降舱出票启用接口
   - 实现操作权限提示

2. **订单查询接口扩展**
   - 在订单详情中显示降舱锁定状态
   - 添加降舱任务关联查询
   - 更新订单列表筛选条件
   - 保持向后兼容性

### 3.4 中间件和装饰器
1. **权限检查中间件**
   - 实现订单锁定检查装饰器
   - 添加到所有订单操作接口
   - 实现友好的错误提示
   - 支持白名单操作

## 阶段四：前端界面开发 (3-4天)

### 4.1 降舱出票管理页面
1. **创建订单列表页面**
   - 显示降舱出票订单
   - 实现状态筛选
   - 添加操作按钮

2. **创建详情页面**
   - 显示订单详细信息
   - 显示降舱配置
   - 显示扫描记录时间线

3. **创建配置页面**
   - 降舱配置表单
   - 参数验证
   - 实时预览

### 4.2 现有页面扩展
1. **订单管理页面扩展**
   - 添加降舱出票操作按钮
   - 更新订单状态显示
   - 添加降舱状态标识

2. **订单详情页面扩展**
   - 显示降舱相关信息
   - 添加降舱操作入口
   - 显示扫描历史

## 阶段五：测试和优化 (2-3天)

### 5.1 单元测试
1. **服务层测试**
   - 测试降舱逻辑
   - 测试截止时间计算
   - 测试异常处理

2. **API接口测试**
   - 测试所有新增接口
   - 测试参数验证
   - 测试错误处理

3. **数据模型测试**
   - 测试模型CRUD操作
   - 测试数据验证
   - 测试关联查询

### 5.2 集成测试
1. **端到端测试**
   - 完整降舱出票流程测试
   - 多订单并发测试
   - 异常场景测试

2. **性能测试**
   - 扫描性能测试
   - 数据库查询优化
   - 并发处理测试

### 5.3 系统优化
1. **性能优化**
   - 数据库查询优化
   - 缓存机制实现
   - 并发控制优化

2. **监控和日志**
   - 添加关键操作日志
   - 实现性能监控
   - 添加告警机制

## 阶段六：部署和上线 (1-2天)

### 6.1 部署准备
1. **环境配置**
   - 更新配置文件
   - 添加新的配置项
   - 验证环境依赖

2. **数据库迁移**
   - 在生产环境执行迁移
   - 验证数据完整性
   - 创建数据备份

### 6.2 上线部署
1. **代码部署**
   - 部署新版本代码
   - 重启相关服务
   - 验证服务状态

2. **功能验证**
   - 验证基础功能
   - 验证API接口
   - 验证前端页面

### 6.3 监控和维护
1. **上线监控**
   - 监控系统性能
   - 监控错误日志
   - 监控业务指标

2. **问题处理**
   - 及时处理问题
   - 收集用户反馈
   - 持续优化改进

## 关键里程碑

1. **第2天**: 完成数据库设计和基础模型
2. **第6天**: 完成核心业务逻辑开发
3. **第9天**: 完成API接口开发
4. **第13天**: 完成前端界面开发
5. **第16天**: 完成测试和优化
6. **第18天**: 完成部署上线

## 风险控制

1. **技术风险**
   - 与现有系统集成复杂度
   - 性能影响评估
   - 数据一致性保证

2. **业务风险**
   - 降舱逻辑准确性
   - 用户体验影响
   - 运营流程变更

3. **时间风险**
   - 开发进度控制
   - 测试时间充足性
   - 上线时间安排

## 质量保证

1. **代码质量**
   - 代码审查机制
   - 单元测试覆盖率
   - 代码规范检查

2. **功能质量**
   - 需求覆盖完整性
   - 用户体验验证
   - 异常处理完善性

3. **系统质量**
   - 性能指标达标
   - 稳定性验证
   - 安全性检查
