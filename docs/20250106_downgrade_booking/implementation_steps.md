# 降舱出票功能实施步骤

## 阶段一：数据库设计和基础模型 (1-2天)

### 1.1 数据库变更
1. **新增订单状态常量**
   - 修改 `app/consts/status.py`
   - 添加降舱出票相关状态枚举
   - 更新状态映射和描述

2. **创建数据库迁移文件**
   - 使用 alembic 创建迁移文件
   - 新增 `downgrade_booking_configs` 表
   - 新增 `downgrade_booking_records` 表
   - 修改 `flight_orders` 表添加降舱相关字段

3. **执行数据库迁移**
   - 在开发环境执行迁移
   - 验证表结构正确性
   - 创建必要的索引

### 1.2 基础数据模型
1. **创建降舱配置模型**
   - 新建 `app/models/downgrade_booking_config.py`
   - 实现基础CRUD方法
   - 添加数据验证逻辑

2. **创建降舱记录模型**
   - 新建 `app/models/downgrade_booking_record.py`
   - 实现记录保存和查询方法
   - 添加JSON字段处理

3. **更新订单模型**
   - 修改 `app/models/flight_order.py`
   - 添加降舱相关字段
   - 更新模型关系

## 阶段二：核心业务逻辑 (3-4天)

### 2.1 降舱出票服务
1. **创建降舱服务类**
   - 新建 `app/services/downgrade_booking_services.py`
   - 实现舱位扫描逻辑
   - 实现降舱条件判断
   - 实现截止时间计算

2. **集成运价查询**
   - 调用现有运价查询API
   - 解析舱位和价格信息
   - 实现舱位比较逻辑

3. **实现出票执行**
   - 集成现有出票流程
   - 实现降舱出票逻辑
   - 添加异常处理机制

### 2.2 任务调度扩展
1. **扩展任务服务**
   - 修改 `app/services/task_services.py`
   - 添加降舱扫描任务构建
   - 实现任务推送逻辑

2. **实现调度器**
   - 创建降舱出票调度器
   - 实现定期扫描机制
   - 添加并发控制

3. **集成现有任务系统**
   - 与现有Celery任务集成
   - 实现任务状态管理
   - 添加任务监控

## 阶段三：API接口开发 (2-3天)

### 3.1 降舱出票管理接口
1. **创建视图文件**
   - 新建 `app/views/downgrade_booking_views.py`
   - 实现启用降舱出票接口
   - 实现取消降舱出票接口

2. **实现查询接口**
   - 降舱出票订单列表查询
   - 降舱出票详情查询
   - 扫描记录查询

3. **实现配置管理接口**
   - 配置创建和更新接口
   - 配置查询接口
   - 配置验证逻辑

### 3.2 数据模式定义
1. **创建请求响应模式**
   - 新建 `app/views/schemas/downgrade_booking_schemas.py`
   - 定义请求参数模式
   - 定义响应数据模式

2. **扩展现有模式**
   - 修改 `app/views/schemas/order_schemas.py`
   - 添加降舱相关字段
   - 更新订单查询响应

### 3.3 扩展现有接口
1. **订单管理接口扩展**
   - 修改 `app/views/order_views.py`
   - 添加推送降舱出票接口
   - 更新订单详情接口

2. **订单查询扩展**
   - 在订单列表中显示降舱状态
   - 添加降舱出票筛选条件
   - 更新状态统计接口

## 阶段四：前端界面开发 (3-4天)

### 4.1 降舱出票管理页面
1. **创建订单列表页面**
   - 显示降舱出票订单
   - 实现状态筛选
   - 添加操作按钮

2. **创建详情页面**
   - 显示订单详细信息
   - 显示降舱配置
   - 显示扫描记录时间线

3. **创建配置页面**
   - 降舱配置表单
   - 参数验证
   - 实时预览

### 4.2 现有页面扩展
1. **订单管理页面扩展**
   - 添加降舱出票操作按钮
   - 更新订单状态显示
   - 添加降舱状态标识

2. **订单详情页面扩展**
   - 显示降舱相关信息
   - 添加降舱操作入口
   - 显示扫描历史

## 阶段五：测试和优化 (2-3天)

### 5.1 单元测试
1. **服务层测试**
   - 测试降舱逻辑
   - 测试截止时间计算
   - 测试异常处理

2. **API接口测试**
   - 测试所有新增接口
   - 测试参数验证
   - 测试错误处理

3. **数据模型测试**
   - 测试模型CRUD操作
   - 测试数据验证
   - 测试关联查询

### 5.2 集成测试
1. **端到端测试**
   - 完整降舱出票流程测试
   - 多订单并发测试
   - 异常场景测试

2. **性能测试**
   - 扫描性能测试
   - 数据库查询优化
   - 并发处理测试

### 5.3 系统优化
1. **性能优化**
   - 数据库查询优化
   - 缓存机制实现
   - 并发控制优化

2. **监控和日志**
   - 添加关键操作日志
   - 实现性能监控
   - 添加告警机制

## 阶段六：部署和上线 (1-2天)

### 6.1 部署准备
1. **环境配置**
   - 更新配置文件
   - 添加新的配置项
   - 验证环境依赖

2. **数据库迁移**
   - 在生产环境执行迁移
   - 验证数据完整性
   - 创建数据备份

### 6.2 上线部署
1. **代码部署**
   - 部署新版本代码
   - 重启相关服务
   - 验证服务状态

2. **功能验证**
   - 验证基础功能
   - 验证API接口
   - 验证前端页面

### 6.3 监控和维护
1. **上线监控**
   - 监控系统性能
   - 监控错误日志
   - 监控业务指标

2. **问题处理**
   - 及时处理问题
   - 收集用户反馈
   - 持续优化改进

## 关键里程碑

1. **第2天**: 完成数据库设计和基础模型
2. **第6天**: 完成核心业务逻辑开发
3. **第9天**: 完成API接口开发
4. **第13天**: 完成前端界面开发
5. **第16天**: 完成测试和优化
6. **第18天**: 完成部署上线

## 风险控制

1. **技术风险**
   - 与现有系统集成复杂度
   - 性能影响评估
   - 数据一致性保证

2. **业务风险**
   - 降舱逻辑准确性
   - 用户体验影响
   - 运营流程变更

3. **时间风险**
   - 开发进度控制
   - 测试时间充足性
   - 上线时间安排

## 质量保证

1. **代码质量**
   - 代码审查机制
   - 单元测试覆盖率
   - 代码规范检查

2. **功能质量**
   - 需求覆盖完整性
   - 用户体验验证
   - 异常处理完善性

3. **系统质量**
   - 性能指标达标
   - 稳定性验证
   - 安全性检查
