# 降舱出票功能设计方案

## 需求概述

降舱出票是指在出票时间宽裕的情况下，通过不断的扫描余票信息以期待在出现低舱位时进行换仓出票的操作。

## 功能要点

1. **前提条件**：保证预占座结果的有效性，确保不会因为等待降舱出票而出现无票或者升舱的情况
2. **截止时间**：降舱出票扫描等待的截止时间有三种：
   - 预占座无法接续
   - 距离出票时限不足1小时（客户催单可能会缩短出票时限）
   - 到达手工设置的截止时间
3. **订单管理**：需要有单独的订单列表，及时掌握所有降舱出票订单的实时信息
4. **操作支持**：降舱出票列表支持取消降舱出票操作

## 业务流程

预占座成功未支付的订单（手工）推送到降舱出票 → 降舱出票成功的订单状态衔接到出票成功待通知平台 → 继续后续流程

## 核心设计要点（插件化方案）

### 1. 插件化架构设计
- **最小侵入原则**：订单表仅新增1个锁定字段，不修改订单状态
- **独立任务管理**：使用专门的任务表管理降舱出票生命周期
- **订单保护机制**：启用降舱时锁定订单，防止其他流程干预
- **可插拔设计**：功能可随时启用或禁用，不影响现有系统

### 2. 数据库设计
- **新增任务表**：`downgrade_booking_tasks` 管理降舱任务状态
- **新增记录表**：`downgrade_booking_scan_records` 记录扫描历史
- **最小修改订单表**：仅添加 `downgrade_booking_enabled` 锁定字段
- **自动迁移**：通过 Alembic 自动生成和执行数据库迁移

### 3. 核心功能模块
- **任务管理器**：创建、更新、取消降舱任务
- **订单锁定器**：管理订单锁定状态，防止冲突操作
- **扫描服务**：定期扫描余票、判断降舱条件、执行出票
- **调度器**：管理任务调度、控制并发、处理超时

### 4. API接口设计
- **插件管理接口**：启用/取消降舱、任务列表、详情查询、配置更新
- **权限保护接口**：在现有订单操作中添加锁定检查
- **向后兼容**：保持现有接口结构不变，仅添加权限检查

### 5. 业务逻辑核心
- **手工触发**：通过管理界面手工启用，不自动触发
- **订单锁定**：启用时锁定订单，禁止其他流程操作
- **任务驱动**：使用任务状态管理，不影响订单状态流转
- **优雅退出**：可随时取消并恢复正常流程

## 技术实现要点

### 1. 文件修改清单（插件化）
```
新增文件：
- app/models/downgrade_booking_task.py (任务模型)
- app/models/downgrade_booking_scan_record.py (扫描记录模型)
- app/services/downgrade_booking_services.py (核心业务逻辑)
- app/services/order_lock_services.py (订单锁定服务)
- app/views/downgrade_booking_views.py (插件管理接口)
- app/views/schemas/downgrade_booking_schemas.py (数据模式)

修改文件：
- app/consts/types.py (新增任务状态枚举)
- app/models/flight_order.py (仅新增1个锁定字段)
- app/services/task_services.py (扩展任务调度)
- app/views/order_views.py (添加权限检查)
- app/decorators.py (新增锁定检查装饰器)
```

### 2. 数据库变更（最小化）
```bash
# 定义好模型后，自动生成迁移文件
alembic revision --autogenerate -m "add_downgrade_booking_plugin"

# 执行迁移，自动创建表和字段
alembic upgrade head

# 结果：新增两个独立表 + 订单表新增1个锁定字段
```

### 3. 任务状态流转（替代订单状态）
```
pending → running → [success/failed/timeout/cancelled]
订单锁定：downgrade_booking_enabled = true/false
```

### 4. 插件化优势
- **最小侵入**：对现有系统影响最小
- **独立管理**：降舱功能完全独立
- **易于维护**：模块化设计，便于扩展
- **可回滚**：可轻松禁用或移除功能

## 设计文档目录

- [数据库设计](./database_design.md) - 数据库表结构设计
- [功能设计](./feature_design.md) - 详细功能设计和修改点
- [流程图](./flowchart.md) - 业务流程图
- [实施步骤](./implementation_steps.md) - 详细实施步骤

## 实施计划

**总工期**：16-18天

1. **阶段一**（1-2天）：数据库设计和基础模型
2. **阶段二**（3-4天）：核心业务逻辑
3. **阶段三**（2-3天）：API接口开发
4. **阶段四**（3-4天）：前端界面开发
5. **阶段五**（2-3天）：测试和优化
6. **阶段六**（1-2天）：部署和上线

## 风险评估

1. **技术风险**：与现有系统集成复杂度中等，需要仔细处理状态转换
2. **业务风险**：降舱逻辑准确性要求高，需要充分测试
3. **性能风险**：定期扫描可能影响系统性能，需要优化并发控制

## 创建时间

2025年1月6日

## 设计版本

v1.0
