# 降舱出票功能设计方案

## 需求概述

降舱出票是指在出票时间宽裕的情况下，通过不断的扫描余票信息以期待在出现低舱位时进行换仓出票的操作。

## 功能要点

1. **前提条件**：保证预占座结果的有效性，确保不会因为等待降舱出票而出现无票或者升舱的情况
2. **截止时间**：降舱出票扫描等待的截止时间有三种：
   - 预占座无法接续
   - 距离出票时限不足1小时（客户催单可能会缩短出票时限）
   - 到达手工设置的截止时间
3. **订单管理**：需要有单独的订单列表，及时掌握所有降舱出票订单的实时信息
4. **操作支持**：降舱出票列表支持取消降舱出票操作

## 业务流程

预占座成功未支付的订单（手工）推送到降舱出票 → 降舱出票成功的订单状态衔接到出票成功待通知平台 → 继续后续流程

## 核心设计要点

### 1. 数据库设计
- **新增订单状态**：6个降舱出票相关状态
- **新增配置表**：`downgrade_booking_configs` 存储降舱配置
- **新增记录表**：`downgrade_booking_records` 记录扫描历史
- **扩展订单表**：添加7个降舱相关字段

### 2. 核心功能模块
- **降舱配置管理**：目标舱位、价格差异、扫描间隔等配置
- **降舱扫描服务**：定期扫描余票、判断降舱条件、执行出票
- **任务调度系统**：管理扫描任务、控制并发、处理超时

### 3. API接口设计
- **管理接口**：启用/取消降舱、订单列表、详情查询、配置更新
- **扩展现有接口**：订单管理中添加降舱推送功能

### 4. 业务逻辑核心
- **触发条件**：预占座成功状态 + 时间余量 + 启用配置 + 可降舱位
- **扫描逻辑**：定期扫描 + 舱位比较 + 价格验证 + 库存检查
- **截止计算**：自动/手工/紧急三种模式
- **执行流程**：条件满足 → 执行出票 → 状态更新 → 记录保存

## 技术实现要点

### 1. 文件修改清单
```
新增文件：
- app/models/downgrade_booking_config.py
- app/models/downgrade_booking_record.py
- app/services/downgrade_booking_services.py
- app/views/downgrade_booking_views.py
- app/views/schemas/downgrade_booking_schemas.py

修改文件：
- app/consts/status.py (新增状态)
- app/models/flight_order.py (新增字段)
- app/services/task_services.py (扩展任务)
- app/views/order_views.py (扩展接口)
- configs/order_state_transition_rules.toml (状态转换)
```

### 2. 数据库变更
```sql
-- 新增两个表
CREATE TABLE downgrade_booking_configs (...)
CREATE TABLE downgrade_booking_records (...)

-- 修改订单表
ALTER TABLE flight_orders ADD COLUMN downgrade_booking_enabled ...
-- 共7个新字段
```

### 3. 状态流转
```
预占座成功(103) → 已推送降舱出票(150) → 降舱出票中(151)
→ [降舱出票成功(152) → 出票成功(130)]
或 [失败/超时/取消 → 出票失败(-130)]
```

## 设计文档目录

- [数据库设计](./database_design.md) - 数据库表结构设计
- [功能设计](./feature_design.md) - 详细功能设计和修改点
- [流程图](./flowchart.md) - 业务流程图
- [实施步骤](./implementation_steps.md) - 详细实施步骤

## 实施计划

**总工期**：16-18天

1. **阶段一**（1-2天）：数据库设计和基础模型
2. **阶段二**（3-4天）：核心业务逻辑
3. **阶段三**（2-3天）：API接口开发
4. **阶段四**（3-4天）：前端界面开发
5. **阶段五**（2-3天）：测试和优化
6. **阶段六**（1-2天）：部署和上线

## 风险评估

1. **技术风险**：与现有系统集成复杂度中等，需要仔细处理状态转换
2. **业务风险**：降舱逻辑准确性要求高，需要充分测试
3. **性能风险**：定期扫描可能影响系统性能，需要优化并发控制

## 创建时间

2025年1月6日

## 设计版本

v1.0
