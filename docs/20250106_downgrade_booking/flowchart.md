# 降舱出票功能流程图

## 1. 主流程图

```mermaid
flowchart TD
    A[预占座成功订单] --> B{是否启用降舱出票?}
    B -->|否| C[正常出票流程]
    B -->|是| D[创建降舱出票配置]
    D --> E[更新订单状态为已推送降舱出票]
    E --> F[启动降舱扫描任务]
    
    F --> G[定期扫描余票信息]
    G --> H{是否发现更低舱位?}
    H -->|否| I{是否到达截止时间?}
    H -->|是| J[验证价格差异]
    
    J --> K{价格差异是否在允许范围?}
    K -->|否| I
    K -->|是| L[检查舱位库存]
    
    L --> M{库存是否充足?}
    M -->|否| I
    M -->|是| N[执行降舱出票]
    
    N --> O{出票是否成功?}
    O -->|是| P[更新状态为降舱出票成功]
    O -->|否| Q[记录出票失败]
    
    Q --> I
    P --> R[衔接到出票成功流程]
    
    I -->|否| S[等待下次扫描]
    I -->|是| T[停止扫描]
    
    S --> G
    T --> U[更新状态为降舱出票超时]
    U --> V[转入正常出票流程]
    
    R --> W[通知平台出票成功]
    V --> C
    W --> X[订单完成]
    C --> X
```

## 2. 降舱扫描详细流程

```mermaid
flowchart TD
    A[开始扫描] --> B[获取订单信息]
    B --> C[获取降舱配置]
    C --> D[调用运价查询API]
    
    D --> E{API调用是否成功?}
    E -->|否| F[记录错误信息]
    E -->|是| G[解析舱位信息]
    
    F --> H[等待重试]
    H --> D
    
    G --> I[比较舱位等级]
    I --> J{是否有更低舱位?}
    J -->|否| K[记录扫描结果]
    J -->|是| L[计算价格差异]
    
    L --> M{价格差异是否合理?}
    M -->|否| K
    M -->|是| N[检查库存数量]
    
    N --> O{库存是否足够?}
    O -->|否| K
    O -->|是| P[标记发现降舱机会]
    
    P --> Q[保存扫描记录]
    K --> Q
    Q --> R[返回扫描结果]
```

## 3. 截止时间计算流程

```mermaid
flowchart TD
    A[开始计算截止时间] --> B{截止类型}
    B -->|auto| C[获取预占座过期时间]
    B -->|manual| D[使用手工设置时间]
    B -->|urgent| E[使用紧急出票时间]
    
    C --> F[计算距离过期剩余时间]
    F --> G{剩余时间是否充足?}
    G -->|是| H[设置为过期前1小时]
    G -->|否| I[立即停止扫描]
    
    D --> J[验证手工时间有效性]
    J --> K{时间是否有效?}
    K -->|是| L[使用手工时间]
    K -->|否| M[使用默认时间]
    
    E --> N[当前时间 + 紧急小时数]
    
    H --> O[返回计算结果]
    I --> O
    L --> O
    M --> O
    N --> O
```

## 4. 订单状态转换流程

```mermaid
stateDiagram-v2
    [*] --> 预占座成功(103)
    预占座成功(103) --> 已推送降舱出票(150): 启用降舱出票
    已推送降舱出票(150) --> 降舱出票中(151): 开始扫描
    已推送降舱出票(150) --> 降舱出票失败(-151): 推送失败
    
    降舱出票中(151) --> 降舱出票成功(152): 发现并成功出票
    降舱出票中(151) --> 降舱出票失败(-151): 出票失败
    降舱出票中(151) --> 降舱出票超时(-152): 到达截止时间
    降舱出票中(151) --> 已取消降舱出票(153): 手工取消
    
    降舱出票成功(152) --> 出票成功(130): 衔接正常流程
    降舱出票失败(-151) --> 出票失败(-130): 转入异常处理
    降舱出票超时(-152) --> 出票失败(-130): 转入异常处理
    已取消降舱出票(153) --> 出票失败(-130): 转入异常处理
    
    出票成功(130) --> [*]
    出票失败(-130) --> [*]
```

## 5. 降舱出票管理界面流程

```mermaid
flowchart TD
    A[降舱出票订单列表] --> B[选择订单]
    B --> C{操作类型}
    
    C -->|查看详情| D[显示订单详情]
    C -->|查看扫描记录| E[显示扫描历史]
    C -->|更新配置| F[修改降舱配置]
    C -->|取消降舱| G[确认取消操作]
    
    D --> H[显示当前状态]
    H --> I[显示配置信息]
    I --> J[显示截止时间]
    
    E --> K[显示扫描时间线]
    K --> L[显示发现的舱位]
    L --> M[显示价格变化]
    
    F --> N[验证配置参数]
    N --> O{参数是否有效?}
    O -->|是| P[保存配置]
    O -->|否| Q[显示错误信息]
    
    G --> R[停止扫描任务]
    R --> S[更新订单状态]
    S --> T[记录操作日志]
    
    P --> U[刷新页面]
    Q --> F
    T --> U
```

## 6. 异常处理流程

```mermaid
flowchart TD
    A[异常发生] --> B{异常类型}
    
    B -->|网络超时| C[记录超时信息]
    B -->|API错误| D[记录API错误]
    B -->|数据异常| E[记录数据异常]
    B -->|系统错误| F[记录系统错误]
    
    C --> G{重试次数是否超限?}
    D --> G
    E --> H[标记需要人工处理]
    F --> H
    
    G -->|否| I[等待重试间隔]
    G -->|是| J[停止当前扫描]
    
    I --> K[执行重试]
    J --> L[记录失败原因]
    H --> L
    
    K --> M[继续正常流程]
    L --> N[发送告警通知]
    N --> O[等待人工处理]
```
