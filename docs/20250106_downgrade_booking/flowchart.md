# 降舱出票功能流程图（插件化方案）

## 1. 主流程图

```mermaid
flowchart TD
    A[预占座成功订单] --> B{手工启用降舱出票?}
    B -->|否| C[正常出票流程]
    B -->|是| D[检查订单是否可锁定]
    D --> E{订单状态是否允许?}
    E -->|否| F[提示错误信息]
    E -->|是| G[创建降舱出票任务]
    G --> H[锁定订单 downgrade_booking_enabled=1]
    H --> I[启动降舱扫描任务]

    I --> J[定期扫描余票信息]
    J --> K{是否发现更低舱位?}
    K -->|否| L{是否到达截止时间?}
    K -->|是| M[验证价格差异]

    M --> N{价格差异是否在允许范围?}
    N -->|否| L
    N -->|是| O[检查舱位库存]

    O --> P{库存是否充足?}
    P -->|否| L
    P -->|是| Q[执行降舱出票]

    Q --> R{出票是否成功?}
    R -->|是| S[更新任务状态为成功]
    R -->|否| T[记录出票失败]

    T --> L
    S --> U[解锁订单 downgrade_booking_enabled=0]
    U --> V[衔接到出票成功流程]

    L -->|否| W[等待下次扫描]
    L -->|是| X[停止扫描]

    W --> Y[记录扫描结果]
    Y --> J
    X --> Z[更新任务状态为超时]
    Z --> AA[解锁订单]
    AA --> BB[转入正常出票流程]

    V --> CC[通知平台出票成功]
    BB --> C
    CC --> DD[订单完成]
    C --> DD

    EE[手工取消降舱出票] --> FF[更新任务状态为已取消]
    FF --> GG[解锁订单]
    GG --> BB
```

## 2. 降舱扫描详细流程

```mermaid
flowchart TD
    A[开始扫描] --> B[获取订单信息]
    B --> C[获取降舱配置]
    C --> D[调用运价查询API]
    
    D --> E{API调用是否成功?}
    E -->|否| F[记录错误信息]
    E -->|是| G[解析舱位信息]
    
    F --> H[等待重试]
    H --> D
    
    G --> I[比较舱位等级]
    I --> J{是否有更低舱位?}
    J -->|否| K[记录扫描结果]
    J -->|是| L[计算价格差异]
    
    L --> M{价格差异是否合理?}
    M -->|否| K
    M -->|是| N[检查库存数量]
    
    N --> O{库存是否足够?}
    O -->|否| K
    O -->|是| P[标记发现降舱机会]
    
    P --> Q[保存扫描记录]
    K --> Q
    Q --> R[返回扫描结果]
```

## 3. 截止时间计算流程

```mermaid
flowchart TD
    A[开始计算截止时间] --> B{截止类型}
    B -->|auto| C[获取预占座过期时间]
    B -->|manual| D[使用手工设置时间]
    B -->|urgent| E[使用紧急出票时间]
    
    C --> F[计算距离过期剩余时间]
    F --> G{剩余时间是否充足?}
    G -->|是| H[设置为过期前1小时]
    G -->|否| I[立即停止扫描]
    
    D --> J[验证手工时间有效性]
    J --> K{时间是否有效?}
    K -->|是| L[使用手工时间]
    K -->|否| M[使用默认时间]
    
    E --> N[当前时间 + 紧急小时数]
    
    H --> O[返回计算结果]
    I --> O
    L --> O
    M --> O
    N --> O
```

## 4. 任务状态转换流程（替代订单状态）

```mermaid
stateDiagram-v2
    [*] --> pending: 创建任务
    pending --> running: 开始扫描
    pending --> cancelled: 手工取消

    running --> success: 发现并成功出票
    running --> failed: 出票失败
    running --> timeout: 到达截止时间
    running --> cancelled: 手工取消

    success --> [*]: 任务完成
    failed --> [*]: 任务完成
    timeout --> [*]: 任务完成
    cancelled --> [*]: 任务完成
```

## 5. 订单锁定状态管理

```mermaid
stateDiagram-v2
    [*] --> 正常状态: downgrade_booking_enabled=0
    正常状态 --> 降舱锁定: 启用降舱出票
    降舱锁定 --> 正常状态: 降舱完成/取消/超时

    降舱锁定 --> 拒绝操作: 其他流程尝试操作
    拒绝操作 --> 降舱锁定: 操作被拒绝
```

## 6. 降舱出票管理界面流程

```mermaid
flowchart TD
    A[降舱出票任务列表] --> B[选择任务]
    B --> C{操作类型}

    C -->|查看详情| D[显示任务详情]
    C -->|查看扫描记录| E[显示扫描历史]
    C -->|更新配置| F[修改任务配置]
    C -->|取消任务| G[确认取消操作]

    D --> H[显示任务状态]
    H --> I[显示配置信息]
    I --> J[显示截止时间]
    I --> K[显示订单锁定状态]

    E --> L[显示扫描时间线]
    L --> M[显示发现的舱位]
    M --> N[显示价格变化]

    F --> O[验证配置参数]
    O --> P{参数是否有效?}
    P -->|是| Q[保存配置]
    P -->|否| R[显示错误信息]

    G --> S[停止扫描任务]
    S --> T[更新任务状态为已取消]
    T --> U[解锁订单]
    U --> V[记录操作日志]

    Q --> W[刷新页面]
    R --> F
    V --> W
```

## 7. 订单操作权限检查流程

```mermaid
flowchart TD
    A[订单操作请求] --> B[检查订单锁定状态]
    B --> C{downgrade_booking_enabled?}
    C -->|false| D[允许操作]
    C -->|true| E[检查操作类型]
    E --> F{是否为降舱相关操作?}
    F -->|是| D
    F -->|否| G[拒绝操作]
    G --> H[返回错误信息: 订单被降舱出票锁定]
    D --> I[执行订单操作]
```

## 6. 异常处理流程

```mermaid
flowchart TD
    A[异常发生] --> B{异常类型}
    
    B -->|网络超时| C[记录超时信息]
    B -->|API错误| D[记录API错误]
    B -->|数据异常| E[记录数据异常]
    B -->|系统错误| F[记录系统错误]
    
    C --> G{重试次数是否超限?}
    D --> G
    E --> H[标记需要人工处理]
    F --> H
    
    G -->|否| I[等待重试间隔]
    G -->|是| J[停止当前扫描]
    
    I --> K[执行重试]
    J --> L[记录失败原因]
    H --> L
    
    K --> M[继续正常流程]
    L --> N[发送告警通知]
    N --> O[等待人工处理]
```
