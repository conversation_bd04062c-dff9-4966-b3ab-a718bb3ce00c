# 降舱出票功能数据库设计（插件化方案）

## 1. 设计原则

采用插件化设计，最小化对现有数据库的修改：
- **不修改订单状态**：保持现有订单状态流转不变
- **独立任务管理**：使用独立的任务表管理降舱出票
- **最小字段修改**：订单表仅新增一个开关字段
- **插件式架构**：降舱功能作为独立模块运行

## 2. 新增降舱出票任务表

创建核心任务表 `downgrade_booking_tasks` 替代订单状态管理：

```sql
CREATE TABLE `downgrade_booking_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `task_status` varchar(32) NOT NULL DEFAULT 'pending' COMMENT '任务状态: pending/running/success/failed/timeout/cancelled',
  `target_cabin_class` varchar(32) NOT NULL DEFAULT '' COMMENT '目标舱位等级',
  `target_cabin_codes` text COMMENT '目标舱位代码列表(JSON)',
  `max_price_diff` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '最大价格差异',
  `scan_interval` int(11) NOT NULL DEFAULT '300' COMMENT '扫描间隔(秒)',
  `deadline_type` varchar(32) NOT NULL DEFAULT 'auto' COMMENT '截止类型: auto/manual/urgent',
  `manual_deadline` datetime DEFAULT NULL COMMENT '手工设置截止时间',
  `urgent_hours` int(11) NOT NULL DEFAULT '1' COMMENT '紧急出票小时数',
  `start_time` datetime DEFAULT NULL COMMENT '任务开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
  `deadline_time` datetime DEFAULT NULL COMMENT '任务截止时间',
  `scan_count` int(11) NOT NULL DEFAULT '0' COMMENT '扫描次数',
  `last_scan_time` datetime DEFAULT NULL COMMENT '最后扫描时间',
  `found_better_cabin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否发现更好舱位',
  `booking_result` text COMMENT '出票结果(JSON)',
  `error_message` text COMMENT '错误信息',
  `created_by` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `created_by_name` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人姓名',
  `cancelled_by` int(11) NOT NULL DEFAULT '0' COMMENT '取消人ID',
  `cancelled_by_name` varchar(32) NOT NULL DEFAULT '' COMMENT '取消人姓名',
  `cancel_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '取消原因',
  `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_deadline_time` (`deadline_time`),
  KEY `idx_created` (`created`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='降舱出票任务表';
```

## 3. 新增降舱出票扫描记录表

```sql
CREATE TABLE `downgrade_booking_scan_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL COMMENT '任务ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `scan_time` datetime NOT NULL COMMENT '扫描时间',
  `scan_result` text COMMENT '扫描结果(JSON)',
  `found_lower_cabin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否发现更低舱位',
  `cabin_info` text COMMENT '舱位信息(JSON)',
  `price_info` text COMMENT '价格信息(JSON)',
  `price_diff` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '价格差异',
  `action_taken` varchar(32) NOT NULL DEFAULT 'continue' COMMENT '采取的行动: continue/book/stop',
  `api_response` text COMMENT 'API响应数据(JSON)',
  `error_message` text COMMENT '错误信息',
  `scan_duration` int(11) NOT NULL DEFAULT '0' COMMENT '扫描耗时(毫秒)',
  `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_scan_time` (`scan_time`),
  KEY `idx_found_lower_cabin` (`found_lower_cabin`),
  FOREIGN KEY (`task_id`) REFERENCES `downgrade_booking_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='降舱出票扫描记录表';
```

## 4. 最小化修改现有表

### 4.1 flight_orders 表仅新增一个字段

```sql
ALTER TABLE `flight_orders` ADD COLUMN `downgrade_booking_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用降舱出票';
```

**字段说明**：
- `downgrade_booking_enabled = 1`：订单启用降舱出票，其他流程不可干预
- `downgrade_booking_enabled = 0`：订单未启用降舱出票，正常流程处理

## 5. 任务状态定义

在 `app/consts/types.py` 中新增任务状态枚举：

```python
class DowngradeBookingTaskStatus(BaseEnum):
    """降舱出票任务状态"""

    PENDING = ('pending', '待开始')
    RUNNING = ('running', '扫描中')
    SUCCESS = ('success', '出票成功')
    FAILED = ('failed', '出票失败')
    TIMEOUT = ('timeout', '扫描超时')
    CANCELLED = ('cancelled', '已取消')
```

## 6. 数据库迁移文件

```bash
cd /home/<USER>/workspace/flight/flight_order
alembic revision --autogenerate -m "add_downgrade_booking_plugin"
alembic upgrade head
```

## 7. 插件化设计优势

1. **最小侵入**：仅修改订单表一个字段
2. **独立管理**：降舱任务完全独立管理
3. **状态隔离**：不影响现有订单状态流转
4. **易于维护**：功能模块化，便于后续扩展
5. **可回滚**：可以轻松禁用或移除功能

## 8. 数据一致性保证

1. **订单锁定机制**：`downgrade_booking_enabled = 1` 时锁定订单
2. **任务唯一性**：每个订单最多只能有一个活跃任务
3. **状态同步**：任务状态变更时同步更新订单字段
4. **事务保证**：关键操作使用数据库事务确保一致性
