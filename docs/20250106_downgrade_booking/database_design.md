# 降舱出票功能数据库设计

## 1. 新增订单状态

在 `app/consts/status.py` 中的 `OrderStatus` 枚举中新增以下状态：

```python
# 降舱出票相关状态
DOWNGRADE_BOOKING_PUSHED = (150, '已推送降舱出票')
DOWNGRADE_BOOKING = (151, '降舱出票中')
DOWNGRADE_BOOKING_SUCCESS = (152, '降舱出票成功')
DOWNGRADE_BOOKING_FAIL = (-151, '降舱出票失败')
DOWNGRADE_BOOKING_TIMEOUT = (-152, '降舱出票超时')
DOWNGRADE_BOOKING_CANCELLED = (153, '已取消降舱出票')
```

## 2. 新增降舱出票配置表

创建新表 `downgrade_booking_configs` 用于存储降舱出票配置：

```sql
CREATE TABLE `downgrade_booking_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `target_cabin_class` varchar(32) NOT NULL DEFAULT '' COMMENT '目标舱位等级',
  `target_cabin_codes` text COMMENT '目标舱位代码列表(JSON)',
  `max_price_diff` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '最大价格差异',
  `scan_interval` int(11) NOT NULL DEFAULT '300' COMMENT '扫描间隔(秒)',
  `deadline_type` varchar(32) NOT NULL DEFAULT 'auto' COMMENT '截止类型: auto/manual/urgent',
  `manual_deadline` datetime DEFAULT NULL COMMENT '手工设置截止时间',
  `urgent_hours` int(11) NOT NULL DEFAULT '1' COMMENT '紧急出票小时数',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created` (`created`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='降舱出票配置表';
```

## 3. 新增降舱出票记录表

创建新表 `downgrade_booking_records` 用于记录降舱出票扫描历史：

```sql
CREATE TABLE `downgrade_booking_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `scan_time` datetime NOT NULL COMMENT '扫描时间',
  `scan_result` text COMMENT '扫描结果(JSON)',
  `found_lower_cabin` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否发现更低舱位',
  `cabin_info` text COMMENT '舱位信息(JSON)',
  `price_info` text COMMENT '价格信息(JSON)',
  `action_taken` varchar(32) NOT NULL DEFAULT 'continue' COMMENT '采取的行动: continue/book/stop',
  `error_message` text COMMENT '错误信息',
  `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_scan_time` (`scan_time`),
  KEY `idx_found_lower_cabin` (`found_lower_cabin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='降舱出票扫描记录表';
```

## 4. 修改现有表结构

### 4.1 flight_orders 表新增字段

```sql
ALTER TABLE `flight_orders` ADD COLUMN `downgrade_booking_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用降舱出票';
ALTER TABLE `flight_orders` ADD COLUMN `downgrade_booking_start_time` datetime DEFAULT NULL COMMENT '降舱出票开始时间';
ALTER TABLE `flight_orders` ADD COLUMN `downgrade_booking_end_time` datetime DEFAULT NULL COMMENT '降舱出票结束时间';
ALTER TABLE `flight_orders` ADD COLUMN `downgrade_booking_deadline` datetime DEFAULT NULL COMMENT '降舱出票截止时间';
ALTER TABLE `flight_orders` ADD COLUMN `original_cabin_class` varchar(32) NOT NULL DEFAULT '' COMMENT '原始舱位等级';
ALTER TABLE `flight_orders` ADD COLUMN `original_cabin_code` varchar(8) NOT NULL DEFAULT '' COMMENT '原始舱位代码';
ALTER TABLE `flight_orders` ADD COLUMN `downgrade_scan_count` int(11) NOT NULL DEFAULT '0' COMMENT '降舱扫描次数';
```

## 5. 状态转换规则

在 `configs/order_state_transition_rules.toml` 中新增状态转换规则：

```toml
# 降舱出票状态转换
103 = [150]  # 预占座成功 -> 已推送降舱出票
150 = [151, -151]  # 已推送降舱出票 -> 降舱出票中/降舱出票失败
151 = [152, -151, -152, 153]  # 降舱出票中 -> 成功/失败/超时/取消
152 = [130]  # 降舱出票成功 -> 出票成功
153 = [-130]  # 已取消降舱出票 -> 出票失败
-151 = [-130]  # 降舱出票失败 -> 出票失败
-152 = [-130]  # 降舱出票超时 -> 出票失败
```

## 6. 数据库迁移文件

需要创建 Alembic 迁移文件来执行以上数据库变更：

```bash
cd /home/<USER>/workspace/flight/flight_order
alembic revision --autogenerate -m "add_downgrade_booking_feature"
alembic upgrade head
```
