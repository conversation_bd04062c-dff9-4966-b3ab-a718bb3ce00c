# 降舱出票功能详细设计（插件化方案）

## 1. 插件化架构设计

### 1.1 设计理念
- **插件式架构**：降舱出票作为独立插件，不影响现有订单流程
- **最小侵入**：仅在订单表增加一个开关字段
- **状态隔离**：使用独立任务表管理，不修改订单状态
- **可插拔性**：可以随时启用或禁用功能

### 1.2 核心组件

```
降舱出票插件
├── 任务管理器 (DowngradeBookingTaskManager)
├── 扫描服务 (DowngradeBookingScanService)
├── 调度器 (DowngradeBookingScheduler)
└── 订单锁定器 (OrderLockManager)
```

## 2. 核心功能模块

### 2.1 降舱出票任务管理

**文件位置**: `app/models/downgrade_booking_task.py`

**功能描述**:
- 管理降舱出票任务的完整生命周期
- 替代订单状态，使用独立任务状态管理
- 支持任务创建、更新、取消、查询

**主要方法**:
- `create_task()` - 创建降舱出票任务
- `update_task_status()` - 更新任务状态
- `cancel_task()` - 取消任务
- `get_active_tasks()` - 获取活跃任务
- `get_task_by_order()` - 根据订单号获取任务

### 2.2 降舱出票扫描服务

**文件位置**: `app/services/downgrade_booking_services.py`

**功能描述**:
- 执行降舱出票的核心业务逻辑
- 定期扫描余票信息并记录
- 判断降舱条件并执行出票
- 管理任务状态转换

**主要方法**:
- `scan_available_cabins()` - 扫描可用舱位
- `check_downgrade_conditions()` - 检查降舱条件
- `execute_downgrade_booking()` - 执行降舱出票
- `calculate_deadline()` - 计算截止时间
- `should_stop_scanning()` - 判断是否停止扫描
- `record_scan_result()` - 记录扫描结果

### 2.3 订单锁定管理

**文件位置**: `app/services/order_lock_services.py`

**功能描述**:
- 管理订单的降舱出票锁定状态
- 防止其他流程干预降舱出票订单
- 提供锁定检查和解锁功能

**主要方法**:
- `lock_order_for_downgrade()` - 锁定订单用于降舱出票
- `unlock_order_from_downgrade()` - 解锁订单
- `is_order_locked_for_downgrade()` - 检查订单是否被锁定
- `validate_order_operation()` - 验证订单操作权限

### 2.4 降舱出票调度器

**文件位置**: `app/services/task_services.py` (扩展现有文件)

**功能描述**:
- 管理降舱出票任务的调度
- 定期检查和执行扫描任务
- 控制并发数和资源使用

**新增方法**:
- `downgrade_booking_scheduler()` - 降舱出票调度器
- `build_downgrade_scan_task()` - 构建降舱扫描任务
- `do_downgrade_scan_push()` - 推送降舱扫描任务
- `cleanup_expired_tasks()` - 清理过期任务

## 3. API接口设计

### 3.1 降舱出票管理接口

**文件位置**: `app/views/downgrade_booking_views.py`

**接口列表**:

1. **启用降舱出票**
   - `POST /api/v1/downgrade_booking/enable`
   - 为指定订单创建降舱出票任务并锁定订单

2. **取消降舱出票**
   - `POST /api/v1/downgrade_booking/cancel`
   - 取消指定订单的降舱出票任务并解锁订单

3. **降舱出票任务列表**
   - `POST /api/v1/downgrade_booking/tasks`
   - 查询降舱出票任务列表（替代订单状态查询）

4. **降舱出票任务详情**
   - `POST /api/v1/downgrade_booking/task_detail`
   - 获取任务详细信息和扫描记录

5. **更新降舱任务配置**
   - `POST /api/v1/downgrade_booking/update_task`
   - 更新降舱出票任务配置

6. **扫描记录查询**
   - `POST /api/v1/downgrade_booking/scan_records`
   - 查询指定任务的扫描历史记录

### 3.2 现有接口扩展

**文件位置**: `app/views/order_views.py` (扩展现有文件)

**新增接口**:
1. **推送降舱出票**
   - `POST /api/v1/order/enable_downgrade_booking`
   - 手工为订单启用降舱出票功能

**修改接口**:
1. **订单操作权限检查**
   - 在所有订单操作接口中增加降舱锁定检查
   - 被锁定的订单不允许其他操作

### 3.3 订单锁定检查中间件

**文件位置**: `app/decorators.py` (扩展现有文件)

**新增装饰器**:
```python
def check_downgrade_lock(func):
    """检查订单是否被降舱出票锁定"""
    async def wrapper(*args, **kwargs):
        # 检查订单锁定状态
        # 如果被锁定则拒绝操作
        pass
    return wrapper
```

## 4. 数据模型设计

### 4.1 降舱出票任务模型

**文件位置**: `app/models/downgrade_booking_task.py`

```python
class DowngradeBookingTask(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "downgrade_booking_tasks"

    order_no = Column(String(64), nullable=False, unique=True, comment="订单号")
    task_status = Column(String(32), nullable=False, default='pending', comment="任务状态")
    target_cabin_class = Column(String(32), nullable=False, comment="目标舱位等级")
    target_cabin_codes = Column(TEXT, comment="目标舱位代码列表(JSON)")
    max_price_diff = Column(DECIMAL(20, 2), nullable=False, comment="最大价格差异")
    scan_interval = Column(Integer, nullable=False, default=300, comment="扫描间隔(秒)")
    deadline_type = Column(String(32), nullable=False, default='auto', comment="截止类型")
    manual_deadline = Column(DATETIME, comment="手工设置截止时间")
    urgent_hours = Column(Integer, nullable=False, default=1, comment="紧急出票小时数")
    start_time = Column(DATETIME, comment="任务开始时间")
    end_time = Column(DATETIME, comment="任务结束时间")
    deadline_time = Column(DATETIME, comment="任务截止时间")
    scan_count = Column(Integer, nullable=False, default=0, comment="扫描次数")
    last_scan_time = Column(DATETIME, comment="最后扫描时间")
    found_better_cabin = Column(Boolean, nullable=False, default=False, comment="是否发现更好舱位")
    booking_result = Column(TEXT, comment="出票结果(JSON)")
    error_message = Column(TEXT, comment="错误信息")
    created_by = Column(Integer, nullable=False, default=0, comment="创建人ID")
    created_by_name = Column(String(32), nullable=False, default='', comment="创建人姓名")
    cancelled_by = Column(Integer, nullable=False, default=0, comment="取消人ID")
    cancelled_by_name = Column(String(32), nullable=False, default='', comment="取消人姓名")
    cancel_reason = Column(String(255), nullable=False, default='', comment="取消原因")
```

### 4.2 降舱出票扫描记录模型

**文件位置**: `app/models/downgrade_booking_scan_record.py`

```python
class DowngradeBookingScanRecord(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "downgrade_booking_scan_records"

    task_id = Column(Integer, nullable=False, comment="任务ID")
    order_no = Column(String(64), nullable=False, comment="订单号")
    scan_time = Column(DATETIME, nullable=False, comment="扫描时间")
    scan_result = Column(TEXT, comment="扫描结果(JSON)")
    found_lower_cabin = Column(Boolean, nullable=False, default=False, comment="是否发现更低舱位")
    cabin_info = Column(TEXT, comment="舱位信息(JSON)")
    price_info = Column(TEXT, comment="价格信息(JSON)")
    price_diff = Column(DECIMAL(20, 2), nullable=False, default=0, comment="价格差异")
    action_taken = Column(String(32), nullable=False, default='continue', comment="采取的行动")
    api_response = Column(TEXT, comment="API响应数据(JSON)")
    error_message = Column(TEXT, comment="错误信息")
    scan_duration = Column(Integer, nullable=False, default=0, comment="扫描耗时(毫秒)")
```

### 4.3 订单模型扩展

**文件位置**: `app/models/flight_order.py` (扩展现有文件)

```python
# 在现有 FlightOrder 类中仅新增一个字段
downgrade_booking_enabled = Column(Boolean, nullable=False, default=False, comment="是否启用降舱出票")
```

### 4.4 模型注册和迁移

**重要说明**：
1. **模型定义完成后**，Alembic 会自动检测模型变更
2. **自动生成迁移**：`alembic revision --autogenerate -m "add_downgrade_booking_plugin"`
3. **自动执行迁移**：`alembic upgrade head`
4. **无需手工编写 SQL**：所有表结构、索引、外键约束都会自动创建

**模型导入**：
确保新模型在 `app/models/__init__.py` 中正确导入，以便 Alembic 能够检测到：

```python
# app/models/__init__.py
from .downgrade_booking_task import DowngradeBookingTask
from .downgrade_booking_scan_record import DowngradeBookingScanRecord
```

## 5. 业务逻辑设计

### 5.1 插件化业务流程

1. **订单锁定机制**: 启用降舱出票时锁定订单，防止其他流程干预
2. **任务独立管理**: 使用任务表管理状态，不影响订单状态流转
3. **插件式触发**: 通过API手工启用，不自动触发
4. **优雅退出**: 可随时取消并恢复正常流程

### 5.2 降舱出票触发条件

1. **订单状态**: 必须是预占座成功(103)状态
2. **锁定状态**: 订单未被其他流程锁定
3. **时间条件**: 距离出发时间有足够余量
4. **手工启用**: 通过管理界面手工启用

### 5.3 扫描逻辑

1. **定期扫描**: 根据任务配置的扫描间隔执行
2. **舱位比较**: 比较当前舱位与目标舱位等级
3. **价格验证**: 确保价格差异在允许范围内
4. **库存检查**: 确认目标舱位有足够库存
5. **记录保存**: 每次扫描都记录详细信息

### 5.4 截止时间计算

1. **自动模式**: 基于预占座过期时间和紧急出票时间
2. **手工模式**: 使用手工设置的截止时间
3. **紧急模式**: 客户催单时缩短出票时限

### 5.5 降舱出票执行

1. **条件满足**: 发现符合条件的低舱位
2. **任务锁定**: 防止重复执行
3. **执行出票**: 调用现有出票流程
4. **状态更新**: 更新任务状态为成功
5. **订单解锁**: 解除订单锁定状态
6. **记录保存**: 保存完整的出票记录

### 5.6 订单保护机制

1. **锁定检查**: 所有订单操作前检查是否被降舱锁定
2. **权限验证**: 只有降舱功能可以操作被锁定的订单
3. **状态同步**: 任务状态变更时同步更新订单锁定状态
4. **异常恢复**: 异常情况下自动解锁订单

## 5. 异常处理

### 5.1 扫描异常

- 网络超时: 记录错误，继续下次扫描
- API错误: 记录错误信息，重试机制
- 数据异常: 记录异常数据，人工介入

### 5.2 出票异常

- 出票失败: 回滚到原始状态，继续扫描
- 舱位变化: 重新评估降舱条件
- 价格变化: 重新计算价格差异

### 5.3 超时处理

- 扫描超时: 自动停止扫描，转入正常出票流程
- 配置超时: 根据配置策略处理
- 系统超时: 记录日志，人工处理

## 6. 性能优化

### 6.1 扫描优化

- 批量处理: 同时扫描多个订单
- 缓存机制: 缓存舱位信息减少API调用
- 智能间隔: 根据历史数据调整扫描频率

### 6.2 数据库优化

- 索引优化: 为查询字段添加合适索引
- 分页查询: 大量数据时使用分页
- 定期清理: 清理过期的扫描记录

### 6.3 并发控制

- 锁机制: 防止同一订单重复扫描
- 限流控制: 控制API调用频率
- 资源管理: 合理分配系统资源
