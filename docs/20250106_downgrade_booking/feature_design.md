# 降舱出票功能详细设计

## 1. 核心功能模块

### 1.1 降舱出票配置管理

**文件位置**: `app/models/downgrade_booking_config.py`

**功能描述**: 
- 管理降舱出票的配置信息
- 支持目标舱位设置、价格差异限制、扫描间隔等配置
- 支持多种截止时间策略

**主要方法**:
- `create_config()` - 创建降舱出票配置
- `update_config()` - 更新配置
- `get_active_configs()` - 获取激活的配置
- `deactivate_config()` - 停用配置

### 1.2 降舱出票扫描服务

**文件位置**: `app/services/downgrade_booking_services.py`

**功能描述**:
- 执行降舱出票的核心逻辑
- 定期扫描余票信息
- 判断是否满足降舱条件
- 执行降舱出票操作

**主要方法**:
- `scan_available_cabins()` - 扫描可用舱位
- `check_downgrade_conditions()` - 检查降舱条件
- `execute_downgrade_booking()` - 执行降舱出票
- `calculate_deadline()` - 计算截止时间
- `should_stop_scanning()` - 判断是否停止扫描

### 1.3 降舱出票任务调度

**文件位置**: `app/services/task_services.py` (扩展现有文件)

**功能描述**:
- 管理降舱出票任务的调度
- 定期检查需要降舱出票的订单
- 控制扫描频率和并发数

**新增方法**:
- `downgrade_booking_scheduler()` - 降舱出票调度器
- `build_downgrade_scan_task()` - 构建降舱扫描任务
- `do_downgrade_scan_push()` - 推送降舱扫描任务

## 2. API接口设计

### 2.1 降舱出票管理接口

**文件位置**: `app/views/downgrade_booking_views.py`

**接口列表**:

1. **启用降舱出票**
   - `POST /api/v1/downgrade_booking/enable`
   - 为指定订单启用降舱出票功能

2. **取消降舱出票**
   - `POST /api/v1/downgrade_booking/cancel`
   - 取消指定订单的降舱出票

3. **降舱出票订单列表**
   - `POST /api/v1/downgrade_booking/search`
   - 查询降舱出票订单列表

4. **降舱出票详情**
   - `POST /api/v1/downgrade_booking/detail`
   - 获取降舱出票详细信息和扫描记录

5. **更新降舱配置**
   - `POST /api/v1/downgrade_booking/update_config`
   - 更新降舱出票配置

### 2.2 现有接口扩展

**文件位置**: `app/views/order_views.py` (扩展现有文件)

**新增接口**:
1. **推送降舱出票**
   - `POST /api/v1/order/push_downgrade_booking`
   - 手工推送订单到降舱出票

## 3. 数据模型设计

### 3.1 降舱出票配置模型

**文件位置**: `app/models/downgrade_booking_config.py`

```python
class DowngradeBookingConfig(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "downgrade_booking_configs"
    
    order_no = Column(String(64), nullable=False, unique=True, comment="订单号")
    target_cabin_class = Column(String(32), nullable=False, comment="目标舱位等级")
    target_cabin_codes = Column(TEXT, comment="目标舱位代码列表(JSON)")
    max_price_diff = Column(DECIMAL(20, 2), nullable=False, comment="最大价格差异")
    scan_interval = Column(Integer, nullable=False, default=300, comment="扫描间隔(秒)")
    deadline_type = Column(String(32), nullable=False, default='auto', comment="截止类型")
    manual_deadline = Column(DATETIME, comment="手工设置截止时间")
    urgent_hours = Column(Integer, nullable=False, default=1, comment="紧急出票小时数")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否激活")
```

### 3.2 降舱出票记录模型

**文件位置**: `app/models/downgrade_booking_record.py`

```python
class DowngradeBookingRecord(BaseDBModel, MixinFields, MixinFunctions):
    __tablename__ = "downgrade_booking_records"
    
    order_no = Column(String(64), nullable=False, comment="订单号")
    scan_time = Column(DATETIME, nullable=False, comment="扫描时间")
    scan_result = Column(TEXT, comment="扫描结果(JSON)")
    found_lower_cabin = Column(Boolean, nullable=False, default=False, comment="是否发现更低舱位")
    cabin_info = Column(TEXT, comment="舱位信息(JSON)")
    price_info = Column(TEXT, comment="价格信息(JSON)")
    action_taken = Column(String(32), nullable=False, default='continue', comment="采取的行动")
    error_message = Column(TEXT, comment="错误信息")
```

## 4. 业务逻辑设计

### 4.1 降舱出票触发条件

1. **订单状态**: 必须是预占座成功(103)状态
2. **时间条件**: 距离出发时间有足够余量
3. **配置条件**: 订单启用了降舱出票功能
4. **舱位条件**: 存在可降级的目标舱位

### 4.2 扫描逻辑

1. **定期扫描**: 根据配置的扫描间隔执行
2. **舱位比较**: 比较当前舱位与目标舱位
3. **价格验证**: 确保价格差异在允许范围内
4. **库存检查**: 确认目标舱位有足够库存

### 4.3 截止时间计算

1. **自动模式**: 基于预占座过期时间和紧急出票时间
2. **手工模式**: 使用手工设置的截止时间
3. **紧急模式**: 客户催单时缩短出票时限

### 4.4 降舱出票执行

1. **条件满足**: 发现符合条件的低舱位
2. **执行出票**: 调用现有出票流程
3. **状态更新**: 更新订单状态为降舱出票成功
4. **记录保存**: 保存降舱出票记录

## 5. 异常处理

### 5.1 扫描异常

- 网络超时: 记录错误，继续下次扫描
- API错误: 记录错误信息，重试机制
- 数据异常: 记录异常数据，人工介入

### 5.2 出票异常

- 出票失败: 回滚到原始状态，继续扫描
- 舱位变化: 重新评估降舱条件
- 价格变化: 重新计算价格差异

### 5.3 超时处理

- 扫描超时: 自动停止扫描，转入正常出票流程
- 配置超时: 根据配置策略处理
- 系统超时: 记录日志，人工处理

## 6. 性能优化

### 6.1 扫描优化

- 批量处理: 同时扫描多个订单
- 缓存机制: 缓存舱位信息减少API调用
- 智能间隔: 根据历史数据调整扫描频率

### 6.2 数据库优化

- 索引优化: 为查询字段添加合适索引
- 分页查询: 大量数据时使用分页
- 定期清理: 清理过期的扫描记录

### 6.3 并发控制

- 锁机制: 防止同一订单重复扫描
- 限流控制: 控制API调用频率
- 资源管理: 合理分配系统资源
