# 降舱出票功能设计总结（插件化方案）

## 1. 需求理解总结

基于用户的调整需求，采用插件化设计方案，最小化对现有订单系统的影响。核心思路是使用独立的任务表管理降舱出票，通过订单锁定机制防止冲突，实现功能的可插拔性。

## 2. 插件化技术架构设计

### 2.1 数据层设计（最小侵入）
- **新增2个独立表**：任务表和扫描记录表
- **最小修改订单表**：仅新增1个锁定字段
- **任务状态管理**：6个任务状态替代订单状态修改
- **保持现有结构**：不影响现有订单状态流转

### 2.2 业务层设计（插件式）
- **任务管理器**：独立管理降舱任务生命周期
- **订单锁定器**：防止其他流程干预降舱订单
- **扫描服务**：核心业务逻辑，包含舱位扫描、条件判断、出票执行
- **调度器**：集成到现有任务系统，支持定期扫描和并发控制
- **权限控制**：通过装饰器实现订单操作权限检查

### 2.3 接口层设计（向后兼容）
- **新增插件接口**：6个降舱出票专用管理接口
- **权限保护接口**：在现有订单操作中添加锁定检查
- **保持兼容性**：现有接口结构不变，仅添加权限验证

## 3. 核心业务逻辑（插件化）

### 3.1 插件化触发机制
1. **手工启用**：通过管理界面手工启用，不自动触发
2. **订单锁定**：启用时设置 `downgrade_booking_enabled=1`
3. **权限保护**：锁定后其他流程无法操作订单
4. **任务驱动**：使用任务状态管理，不影响订单状态

### 3.2 扫描逻辑
1. **定期扫描**：根据任务配置间隔执行
2. **舱位比较**：比较当前舱位与目标舱位等级
3. **价格验证**：确保价格差异在允许范围内
4. **库存检查**：确认目标舱位有足够库存
5. **记录保存**：每次扫描都保存详细记录

### 3.3 截止时间策略
1. **自动模式**：基于预占座过期时间计算
2. **手工模式**：使用人工设置的截止时间
3. **紧急模式**：客户催单时的快速出票

### 3.4 执行流程（插件式）
1. 发现符合条件的低舱位
2. 更新任务状态为执行中
3. 调用现有出票流程执行出票
4. 更新任务状态为成功
5. 解锁订单 `downgrade_booking_enabled=0`
6. 衔接到正常的出票成功流程

### 3.5 订单保护机制
1. **锁定检查**：所有订单操作前检查锁定状态
2. **权限验证**：只有降舱功能可操作被锁定订单
3. **状态同步**：任务状态变更时同步订单锁定状态
4. **异常恢复**：异常情况下自动解锁订单

## 4. 关键技术要点（插件化）

### 4.1 插件化集成策略
- **最小侵入**：仅修改订单表1个字段，不影响现有状态系统
- **独立管理**：使用独立任务表管理，与订单状态隔离
- **权限控制**：通过装饰器实现订单操作权限检查
- **向后兼容**：保持现有API接口结构不变

### 4.2 订单锁定机制
- **锁定标识**：`downgrade_booking_enabled` 字段标识锁定状态
- **权限检查**：所有订单操作前检查锁定状态
- **白名单机制**：降舱相关操作可以操作被锁定订单
- **自动解锁**：任务完成/取消/超时时自动解锁

### 4.3 任务状态管理
- **独立状态**：6个任务状态完全独立于订单状态
- **状态转换**：pending → running → [success/failed/timeout/cancelled]
- **状态同步**：任务状态变更时同步订单锁定状态
- **异常恢复**：异常情况下自动恢复到正常状态

### 4.4 性能优化考虑
- **批量处理**：支持同时扫描多个任务
- **缓存机制**：减少重复的API调用
- **并发控制**：防止资源竞争和重复处理
- **智能间隔**：根据历史数据优化扫描频率

### 4.5 异常处理策略
- **网络异常**：重试机制和降级处理
- **出票异常**：回滚机制和订单解锁
- **超时处理**：自动解锁并转入正常流程
- **数据异常**：完整的错误记录和告警

## 5. 实施风险评估（插件化方案）

### 5.1 技术风险（低）
- **集成复杂度**：插件化设计大大降低集成复杂度
- **状态一致性**：使用独立任务状态，避免状态冲突
- **性能影响**：最小化对现有系统的性能影响

**缓解措施**：
- 插件化架构降低集成风险
- 独立任务管理避免状态冲突
- 权限检查装饰器确保操作安全

### 5.2 业务风险（低）
- **降舱逻辑准确性**：复用现有出票流程，降低风险
- **用户体验**：手工启用，用户可控性强
- **运营流程**：最小化对现有流程的影响

**缓解措施**：
- 复用现有成熟的出票流程
- 提供清晰的任务状态反馈
- 渐进式上线和充分培训

### 5.3 时间风险（低）
- **开发进度**：插件化设计简化开发复杂度
- **测试时间**：独立模块便于单独测试
- **上线协调**：最小化对其他系统的影响

**缓解措施**：
- 模块化开发，便于并行进行
- 独立测试，降低测试复杂度
- 插件式部署，降低上线风险

### 5.4 插件化方案优势
- **可回滚性**：功能可随时禁用或移除
- **独立性**：不影响现有系统稳定性
- **可扩展性**：便于后续功能扩展
- **维护性**：模块化设计便于维护

## 6. 成功标准

### 6.1 功能标准
- [ ] 所有降舱出票功能正常工作
- [ ] 与现有系统完全兼容
- [ ] 支持所有定义的业务场景
- [ ] 异常处理机制完善

### 6.2 性能标准
- [ ] 扫描响应时间 < 5秒
- [ ] 系统整体性能影响 < 5%
- [ ] 并发处理能力满足业务需求
- [ ] 数据库查询优化到位

### 6.3 质量标准
- [ ] 单元测试覆盖率 > 90%
- [ ] 集成测试通过率 100%
- [ ] 代码审查通过
- [ ] 文档完整准确

## 7. 后续优化方向

### 7.1 智能化优化
- 基于历史数据的智能扫描间隔调整
- 机器学习预测最佳降舱时机
- 自动化的价格策略优化

### 7.2 用户体验优化
- 实时的扫描状态推送
- 可视化的扫描历史展示
- 移动端支持

### 7.3 运营效率优化
- 批量操作支持
- 自动化的异常处理
- 详细的业务报表

## 8. 结论

降舱出票功能的设计充分考虑了现有系统的架构和业务流程，通过最小化的侵入性修改实现了完整的功能需求。设计方案技术可行，风险可控，预期能够在规定时间内高质量完成开发和上线。

关键成功因素：
1. 严格按照设计方案执行
2. 充分的测试和验证
3. 与现有系统的良好集成
4. 完善的异常处理和监控

通过本次设计，降舱出票功能将为订单系统提供更加灵活和智能的出票策略，提升用户体验和运营效率。
